/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'copy': {
    width: 41,
    height: 40,
    viewBox: '0 0 41 40',
    data: '<path pid="0" fill-rule="evenodd" clip-rule="evenodd" d="M8.227 29.546V5h19.091v5.454h5.455V35H13.682v-5.454H8.227zm16.364-2.728V7.728H10.955v19.09H24.59zm2.727-13.636v16.364H16.41v2.727h13.637V13.182h-2.728z" _fill="#000"/>'
  }
})