/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'draggable': {
    width: 6,
    height: 10,
    viewBox: '0 0 6 10',
    data: '<path pid="0" d="M2.55 1.625a1.125 1.125 0 11-2.25 0 1.125 1.125 0 012.25 0zM2.55 5A1.125 1.125 0 11.3 5a1.125 1.125 0 012.25 0zM1.425 9.5a1.125 1.125 0 100-2.25 1.125 1.125 0 000 2.25zM5.925 1.625a1.125 1.125 0 11-2.25 0 1.125 1.125 0 012.25 0zM4.8 6.125a1.125 1.125 0 100-2.25 1.125 1.125 0 000 2.25zM5.925 8.375a1.125 1.125 0 11-2.25 0 1.125 1.125 0 012.25 0z" _fill="#4C4EA3"/>'
  }
})