name: Publish documentation

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  workflow_dispatch:

  push:
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+"
    branches:
      - "main"
      - "develop"
      - "docs/**"
    paths:
      - ".github/workflows/extralit.docs.yml"
      - "extralit/docs/**"
      - "extralit/mkdocs.yml"

defaults:
  run:
    working-directory: extralit

permissions:
  contents: write
  pull-requests: write

jobs:
  publish:
    runs-on: ubuntu-latest

    env:
      GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}

    steps:
      - name: checkout docs-site
        uses: actions/checkout@v4
        with:
          ref: gh-pages

      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          pyproject-file: "extralit/pyproject.toml"
          python-version: "3.10"
          enable-cache: true
          cache-local-path: ~/.cache/uv
          ignore-nothing-to-cache: true
          cache-dependency-glob: "extralit/pdm.lock"

      - name: Setup PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version: "3.10"
          cache: true
          cache-dependency-path: |
            extralit/pdm.lock

      - name: Install dependencies
        env:
          PDM_IGNORE_ACTIVE_VENV: 1
        run: |
          pdm config use_uv true
          pdm config python.install_root "$(uv python dir)"
          pdm install -q
          uv cache prune --ci

      - name: Set git credentials
        run: |
          git config --global user.name "${{ github.actor }}"
          git config --global user.email "${{ github.actor }}@users.noreply.github.com"

      - name: Print GitHub ref info
        run: echo "${{ github.ref }}"
          echo "${{ github.head_ref }}"

      - name: Deploy Extralit docs (branch /main)
        run: |
          pdm run mike deploy stable --push
        if: github.ref == 'refs/heads/main'

      - name: Deploy Extralit docs (branch /develop)
        run: |
          pdm run mike deploy latest --push
          pdm run mike set-default --push latest
        if: github.ref == 'refs/heads/develop' || github.event_name == 'workflow_dispatch'

      - name: Deploy Extralit docs (release $version)
        run: |
          version=$(echo $TAG_VERSION | awk -F \. {'print $1"."$2'})
          echo "Deploying version ${version}"
          pdm run mike deploy $version --push
        if: startsWith(github.ref, 'refs/tags/')
        env:
          TAG_VERSION: ${{ github.ref_name }}

      - name: Delete Extralit docs (versions 'docs_*')
        id: delete_deployment
        run: |
          versions=$(pdm run mike list)
          formatted_versions=$(echo "$versions" | tr ',' '\n' | tr -d '[]')
          for version in $versions; do
            if [[ $version == docs_* ]]; then
              echo "Deleting version: $version"
              pdm run mike delete "$version" --push
            fi
          done
        if: startsWith(github.ref, 'refs/tags/')

      - name: Extract branch name
        id: extract_branch_name
        shell: bash
        run: echo "branch_name=${GITHUB_HEAD_REF:-${GITHUB_REF#refs/heads/}}" | sed 's/\//_/g'  >> $GITHUB_OUTPUT

      - name: Deploy Extralit docs (branch /docs/*)
        run: pdm run mike deploy ${{ steps.extract_branch_name.outputs.branch_name }} --prop-set hidden=true --push
        if: startsWith(github.ref, 'refs/heads/docs') || startsWith(github.head_ref, 'docs/')

      - name: Add deployment message in PR
        uses: mshick/add-pr-comment@v2
        if: startsWith(github.ref, 'refs/heads/docs') || startsWith(github.head_ref, 'docs/')
        with:
          message: |
            Docs for this PR have been deployed hidden from versioning: [https://docs.extralit.ai/${{ steps.extract_branch_name.outputs.branch_name }}](https://docs.extralit.ai/${{ steps.extract_branch_name.outputs.branch_name }})
