---
description: Frequently Asked Questions about Extralit for scientific data extraction.
hide: toc
---

# Extralit FAQ

??? Question "What is Extralit?"

    Extralit is a tool that helps you extract structured data from scientific papers. It uses advanced AI models to make the process faster and more accurate, so you can focus on your research instead of manual data collection.

??? Question "Who should use Extralit?"

    Extralit is designed for scientists, research assistants, and anyone who needs to collect data from academic literature. If you need to build datasets from research papers for analysis or review, Extralit can help.

??? Question "Do I need to know programming or English well to use Extralit?"

    No. Extralit provides an easy-to-use web interface. You do not need to write code or have strong English skills to extract data. The tool guides you step by step.

??? Question "What kind of data can Extralit extract?"

    Extralit can extract many types of information, such as tables, results, study characteristics, and other details from research papers. You can define what data you want to collect using simple templates.

??? Question "Can Extralit handle non-English papers?"

    Yes, Extralit supports many languages. You can extract data from papers written in different languages, depending on the AI model used.

??? Question "How does Extralit ensure data quality?"

    Extralit uses AI to suggest data extractions, but you can review and correct the results. This human-in-the-loop approach helps ensure the extracted data is accurate and reliable.

??? Question "Is my data safe with Extralit?"

    Yes. Extralit can be run on your own computer or server, so your data stays private. You control where your data is stored.

??? Question "Do I need to install anything to use Extralit?"

    You can use Extralit through a web browser. There is also a public demo available online. For more control, you can install Extralit on your own system.

??? Question "How much does Extralit cost?"

    Extralit is open-source and free to use.

??? Question "Where can I get help or support?"

    You can find guides and documentation on the Extralit website. There is also a community where you can ask questions and get help from other users.

??? Question "Can Extralit process PDF files directly?"

    Yes, Extralit can process PDF files directly. Upload your research papers, and Extralit will automatically parse the content, including text, tables, and figures.

??? Question "How do I define what data to extract?"

    Extralit uses extraction schemas - templates that define what information you want to collect. You can create custom schemas or use existing ones for common research data types.

??? Question "Can I use Extralit for systematic reviews or meta-analyses?"

    Yes, Extralit is especially useful for systematic reviews and meta-analyses. It helps standardize data extraction across multiple papers and can handle large document collections efficiently.

??? Question "How does Extralit handle tables in research papers?"

    Extralit has specialized capabilities for extracting and structuring tabular data from research papers. It can recognize table structures even when they span multiple pages or have complex layouts.

??? Question "Can I collaborate with my research team using Extralit?"

    Yes, Extralit supports collaborative workflows. Multiple team members can work on the same project, review each other's extractions, and maintain consistent data quality.