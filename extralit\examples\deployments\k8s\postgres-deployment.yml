# postgres-deployment.yml

apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
spec:
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
        - name: postgres
          image: postgres:14-alpine
          ports:
            - containerPort: 5432
          env:
            - name: POSTGRES_DB
              value: extralit
            - name: POSTGRES_USER
              value: extralit
            - name: POSTGRES_PASSWORD
              value: password
          volumeMounts:
            - mountPath: /var/lib/postgresql/data
              name: postgresdb
      volumes:
        - name: postgresdb
          persistentVolumeClaim:
            claimName: postgres-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: local-path
  resources:
    requests:
      storage: 1Gi
  volumeName: postgres-pv
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: postgres-pv
spec:
  capacity:
    storage: 1Gi # specify the size of the volume
  volumeMode: Filesystem # can also be Block
  accessModes:
    - ReadWriteOnce # The volume can be mounted as read-write by a single node
  storageClassName: local-path # name of the StorageClass
  persistentVolumeReclaimPolicy: Retain # Retain the volume data when the PVC is deleted
