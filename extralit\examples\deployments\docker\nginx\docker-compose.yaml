version: "3.8"

services:
  nginx:
    image: nginx:latest
    container_name: "nginx_proxy"
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro

  extralit:
    image: extralitdev/extralit-hf-space:latest
    environment:
      HF_HUB_DISABLE_TELEMETRY: 1
      EXTRALIT_BASE_URL: /extralit

      USERNAME: extralit
      PASSWORD: 12345678
      API_KEY: extralit.apikey
    ports:
      - "6900:6900"

