apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: extralit-server-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  tls:
    - hosts:
        - extralit-hostname
      secretName: extralit-server-tls
  rules:
    - host: extralit-hostname
      http:
        paths:
          - path: "/"
            pathType: Prefix
            backend:
              service:
                name: extralit-server
                port:
                  number: 6900
