name: Build extralit-server docker images

on:
  workflow_call:
    inputs:
      is_release:
        description: "True if the images should be built for release"
        required: true
        type: boolean

      publish_latest:
        description: "True if the images should be published as latest"
        required: true
        type: boolean

jobs:
  build:
    name: Build Extralit server docker images
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version-file: extralit-server/pyproject.toml
          cache-dependency-path: extralit-server/pdm.lock
          cache: true

      - name: Read package info
        id: package-info
        working-directory: extralit-server
        run: |
          PACKAGE_VERSION=$(pdm show --version)
          PACKAGE_NAME=$(pdm show --name)
          echo "PACKAGE_NAME=$PACKAGE_NAME" >> $GITHUB_OUTPUT
          echo "PACKAGE_VERSION=$PACKAGE_VERSION" >> $GITHUB_OUTPUT
          echo "$PACKAGE_NAME==$PACKAGE_VERSION"

      - name: Get Docker image tag from GITHUB_REF
        if: ${{ !inputs.is_release }}
        id: docker-image-tag-from-ref
        uses: ./.github/actions/docker-image-tag-from-ref

      - name: Setup environment variables
        run: |
          if [[ $IS_RELEASE == true ]]; then
            echo "PLATFORMS=linux/amd64,linux/arm64" >> $GITHUB_ENV
            echo "IMAGE_TAG=v$PACKAGE_VERSION" >> $GITHUB_ENV
            echo "SERVER_DOCKER_IMAGE=extralit/extralit-server" >> $GITHUB_ENV
            echo "HF_SPACES_DOCKER_IMAGE=extralit/extralit-hf-space" >> $GITHUB_ENV
            echo "DOCKER_USERNAME=$DOCKER_USERNAME" >> $GITHUB_ENV
            echo "DOCKER_PASSWORD=$DOCKER_PASSWORD" >> $GITHUB_ENV
            echo "PUBLISH_LATEST=$PUBLISH_LATEST" >> $GITHUB_ENV
          else
            echo "PLATFORMS=linux/amd64" >> $GITHUB_ENV
            echo "IMAGE_TAG=$DOCKER_IMAGE_TAG" >> $GITHUB_ENV
            echo "SERVER_DOCKER_IMAGE=extralitdev/extralit-server" >> $GITHUB_ENV
            echo "HF_SPACES_DOCKER_IMAGE=extralitdev/extralit-hf-space" >> $GITHUB_ENV
            echo "DOCKER_USERNAME=$DOCKER_USERNAME_DEV" >> $GITHUB_ENV
            echo "DOCKER_PASSWORD=$DOCKER_PASSWORD_DEV" >> $GITHUB_ENV
            echo "PUBLISH_LATEST=true" >> $GITHUB_ENV
          fi
        env:
          IS_RELEASE: ${{ inputs.is_release }}
          PUBLISH_LATEST: ${{ inputs.publish_latest }}
          PACKAGE_VERSION: ${{ steps.package-info.outputs.PACKAGE_VERSION }}
          DOCKER_IMAGE_TAG: ${{ steps.docker-image-tag-from-ref.outputs.docker-image-tag }}
          DOCKER_USERNAME: ${{ secrets.AR_DOCKER_USERNAME }}
          DOCKER_PASSWORD: ${{ secrets.AR_DOCKER_PASSWORD }}
          DOCKER_USERNAME_DEV: ${{ secrets.AR_DOCKER_USERNAME_DEV }}
          DOCKER_PASSWORD_DEV: ${{ secrets.AR_DOCKER_PASSWORD_DEV }}

      - name: Set up QEMU
        if: ${{ inputs.is_release }}
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to Docker Hub
        uses: docker/login-action@v3
        with:
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ env.DOCKER_PASSWORD }}

      - name: Download python package
        uses: actions/download-artifact@v4
        with:
          name: extralit-server
          path: extralit-server/docker/server/dist

      - name: Build and push `extralit-server` image
        uses: docker/build-push-action@v5
        with:
          context: extralit-server/docker/server
          platforms: ${{ env.PLATFORMS }}
          tags: ${{ env.SERVER_DOCKER_IMAGE }}:${{ env.IMAGE_TAG }}
          labels: ${{ steps.meta.outputs.labels }}
          push: true

      - name: Push latest `extralit-server` image
        if: ${{ env.PUBLISH_LATEST == 'true' }}
        uses: docker/build-push-action@v5
        with:
          context: extralit-server/docker/server
          platforms: ${{ env.PLATFORMS }}
          tags: ${{ env.SERVER_DOCKER_IMAGE }}:latest
          labels: ${{ steps.meta.outputs.labels }}
          push: true

      - name: Trigger HF-Space build
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.GH_ACTIONS_REPOSITORY_DISPATCH }}
          repository: extralit/extralit-hf-space
          event-type: build-hf-space
          client-payload: '{"tag":"${{ env.IMAGE_TAG }}","is_release":${{ inputs.is_release }}}'

      - name: Docker Hub Description for `extralit-server`
        uses: peter-evans/dockerhub-description@v4
        if: ${{ env.TAG_LATEST == 'true' }}
        with:
          username: ${{ env.DOCKER_USERNAME }}
          password: ${{ env.DOCKER_PASSWORD }}
          repository: $${{ env.SERVER_DOCKER_IMAGE }}
          readme-filepath: extralit-server/docker/server/README.md

