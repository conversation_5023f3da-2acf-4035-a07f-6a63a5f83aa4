{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "ImportError", "evalue": "cannot import name 'field_serializer' from 'pydantic' (/Users/<USER>/micromamba/envs/extralit/lib/python3.9/site-packages/pydantic/__init__.cpython-39-darwin.so)", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mImportError\u001b[0m                               Traceback (most recent call last)", "Cell \u001b[0;32mIn[4], line 3\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mdatetime\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m datetime\n\u001b[0;32m----> 3\u001b[0m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m \u001b[38;5;21;<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mas\u001b[39;00m \u001b[38;5;21;01mrg\u001b[39;00m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mdatasets\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m load_dataset\n\u001b[1;32m      6\u001b[0m client \u001b[38;5;241m=\u001b[39m rg\u001b[38;5;241m.\u001b[39mExtralit()\n", "File \u001b[0;32m~/Projects/extralit/argilla/src/argilla/__init__.py:15\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Copyright 2024-present, Extralit, Inc.\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# Licensed under the Apache License, Version 2.0 (the \"License\");\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;66;03m# See the License for the specific language governing permissions and\u001b[39;00m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;66;03m# limitations under the License.\u001b[39;00m\n\u001b[1;32m     14\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_version\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m __version__  \u001b[38;5;66;03m# noqa\u001b[39;00m\n\u001b[0;32m---> 15\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mclient\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m  \u001b[38;5;66;03m# noqa\u001b[39;00m\n\u001b[1;32m     16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01margilla\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdatasets\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m  \u001b[38;5;66;03m# noqa\u001b[39;00m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01margilla\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mworkspaces\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m  \u001b[38;5;66;03m# noqa\u001b[39;00m\n", "File \u001b[0;32m~/Projects/extralit/argilla/src/argilla/client.py:22\u001b[0m\n\u001b[1;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtyping\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m TYPE_CHECKING, List, Optional, Union, overload\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01muuid\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m UUID\n\u001b[0;32m---> 22\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m _api\n\u001b[1;32m     23\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_api\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_base\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ResourceAPI\n\u001b[1;32m     24\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01margilla\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_api\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_client\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DEFAULT_HTTP_CONFIG\n", "File \u001b[0;32m~/Projects/extralit/argilla/src/argilla/_api/__init__.py:15\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Copyright 2024-present, Extralit, Inc.\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# Licensed under the Apache License, Version 2.0 (the \"License\");\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     12\u001b[0m \u001b[38;5;66;03m# See the License for the specific language governing permissions and\u001b[39;00m\n\u001b[1;32m     13\u001b[0m \u001b[38;5;66;03m# limitations under the License.\u001b[39;00m\n\u001b[0;32m---> 15\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_api\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_datasets\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m  \u001b[38;5;66;03m# noqa 403\u001b[39;00m\n\u001b[1;32m     16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01margilla\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_api\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_http\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m  \u001b[38;5;66;03m# noqa 403\u001b[39;00m\n\u001b[1;32m     17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01margilla\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_api\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_workspaces\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;241m*\u001b[39m  \u001b[38;5;66;03m# noqa 403\u001b[39;00m\n", "File \u001b[0;32m~/Projects/extralit/argilla/src/argilla/_api/_datasets.py:21\u001b[0m\n\u001b[1;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_api\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_base\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ResourceAPI\n\u001b[1;32m     20\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_exceptions\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_api\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m api_error_handler\n\u001b[0;32m---> 21\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;<PERSON><PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_models\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DatasetModel\n\u001b[1;32m     23\u001b[0m __all__ \u001b[38;5;241m=\u001b[39m [\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mDatasetsAPI\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m     25\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01margilla\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_models\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_dataset_progress\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m UserProgressModel, DatasetProgressModel\n", "File \u001b[0;32m~/Projects/extralit/argilla/src/argilla/_models/__init__.py:17\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;66;03m# Copyright 2024-present, Extralit, Inc.\u001b[39;00m\n\u001b[1;32m      2\u001b[0m \u001b[38;5;66;03m#\u001b[39;00m\n\u001b[1;32m      3\u001b[0m \u001b[38;5;66;03m# Licensed under the Apache License, Version 2.0 (the \"License\");\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m     15\u001b[0m \u001b[38;5;66;03m# We skip the flake8 check because we are importing all the models and the import order is important\u001b[39;00m\n\u001b[1;32m     16\u001b[0m \u001b[38;5;66;03m# flake8: noqa\u001b[39;00m\n\u001b[0;32m---> 17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_models\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_resource\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ResourceModel\n\u001b[1;32m     18\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01margilla\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_models\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_workspace\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m WorkspaceModel\n\u001b[1;32m     19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01margilla\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_models\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01m_user\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m UserModel, Role\n", "File \u001b[0;32m~/Projects/extralit/argilla/src/argilla/_models/_resource.py:19\u001b[0m\n\u001b[1;32m     16\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtyping\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Optional\n\u001b[1;32m     17\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01muuid\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m UUID\n\u001b[0;32m---> 19\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpydantic\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BaseModel, field_serializer\n\u001b[1;32m     22\u001b[0m \u001b[38;5;28;01mclass\u001b[39;00m \u001b[38;5;21;01mResourceModel\u001b[39;00m(BaseModel):\n\u001b[1;32m     23\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"Base model for all resources (DatasetModel, WorkspaceModel, UserModel, etc.)\"\"\"\u001b[39;00m\n", "\u001b[0;31mImportError\u001b[0m: cannot import name 'field_serializer' from 'pydantic' (/Users/<USER>/micromamba/envs/extralit/lib/python3.9/site-packages/pydantic/__init__.cpython-39-darwin.so)"]}], "source": ["from datetime import datetime\n", "\n", "import extralit as ex\n", "from datasets import load_dataset\n", "\n", "client = ex.Extralit()"]}, {"cell_type": "code", "execution_count": 106, "metadata": {}, "outputs": [], "source": ["for dataset in client.datasets.list():\n", "    dataset.delete()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Chat Field\n"]}, {"cell_type": "code", "execution_count": 108, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/code/argilla/argilla/src/argilla/datasets/_resource.py:203: UserWarning: Workspace not provided. Using default workspace: argilla id: 735cae0d-eb08-45c3-ad79-0a11ad4dd2c2\n", "  warnings.warn(f\"Workspace not provided. Using default workspace: {workspace.name} id: {workspace.id}\")\n"]}, {"data": {"text/plain": ["Dataset(id=UUID('ee5fc998-b475-45a8-86e7-7ff427d43268') inserted_at=datetime.datetime(2024, 8, 23, 10, 46, 50, 148167) updated_at=datetime.datetime(2024, 8, 23, 10, 46, 50, 291527) name='static_chat_20240823124650' status='ready' guidelines=None allow_extra_metadata=False distribution=OverlapTaskDistributionModel(strategy='overlap', min_submitted=1) workspace_id=UUID('735cae0d-eb08-45c3-ad79-0a11ad4dd2c2') last_activity_at=datetime.datetime(2024, 8, 23, 10, 46, 50, 291527))"]}, "execution_count": 108, "metadata": {}, "output_type": "execute_result"}], "source": ["settings = ex.Settings(\n", "    fields=[\n", "        ex.<PERSON><PERSON><PERSON><PERSON>(\n", "            name=\"chosen\",\n", "        ),\n", "        ex.<PERSON><PERSON><PERSON><PERSON>(\n", "            name=\"rejected\",\n", "        ),\n", "    ],\n", "    questions=[\n", "        ex.RatingQuestion(\n", "            \"rating\",\n", "            title=\"How would you rate the conversation?\",\n", "            required=True,\n", "            values=[1, 2, 3, 4, 5],\n", "        ),\n", "        ex.TextQuestion(\n", "            \"improved_chosen\", title=\"Rewrite the chosen conversation\", required=False\n", "        ),\n", "    ],\n", ")\n", "\n", "dataset = ex.Dataset(\n", "    settings=settings,\n", "    name=f\"static_chat_{datetime.now().strftime('%Y%m%d%H%M%S')}\",\n", ")\n", "\n", "dataset.create()"]}, {"cell_type": "code", "execution_count": 109, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/code/argilla/argilla/src/argilla/records/_mapping/_mapper.py:89: UserWarning: Keys ['source', 'chosen_rating', 'chosen_model', 'rejected_rating', 'rejected_model'] in data are not present in the mapping and will be ignored.\n", "  warnings.warn(f\"Keys {unknown_keys} in data are not present in the mapping and will be ignored.\")\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">DatasetRecords: The provided batch size <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">256</span> was normalized. Using value <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>.\n", "</pre>\n"], "text/plain": ["DatasetRecords: The provided batch size \u001b[1;36m256\u001b[0m was normalized. Using value \u001b[1;36m100\u001b[0m.\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Sending records...: 100%|██████████| 1/1 [00:00<00:00,  3.45batch/s]\n"]}, {"data": {"text/plain": ["DatasetRecords(Dataset(id=UUID('ee5fc998-b475-45a8-86e7-7ff427d43268') inserted_at=datetime.datetime(2024, 8, 23, 10, 46, 50, 148167) updated_at=datetime.datetime(2024, 8, 23, 10, 46, 50, 291527) name='static_chat_20240823124650' status='ready' guidelines=None allow_extra_metadata=False distribution=OverlapTaskDistributionModel(strategy='overlap', min_submitted=1) workspace_id=UUID('735cae0d-eb08-45c3-ad79-0a11ad4dd2c2') last_activity_at=datetime.datetime(2024, 8, 23, 10, 46, 50, 291527)))"]}, "execution_count": 109, "metadata": {}, "output_type": "execute_result"}], "source": ["ds = load_dataset(\"argilla/Capybara-Preferences\", split=\"train[:100]\")\n", "dataset.records.log(ds)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Custom Field"]}, {"cell_type": "code", "execution_count": 110, "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset(id=UUID('620fb219-73cb-42c6-bad0-456880a93ab9') inserted_at=datetime.datetime(2024, 8, 23, 10, 46, 58, 842638) updated_at=datetime.datetime(2024, 8, 23, 10, 46, 59, 10418) name='interactive_chat_20240823124658' status='ready' guidelines=None allow_extra_metadata=False distribution=OverlapTaskDistributionModel(strategy='overlap', min_submitted=1) workspace_id=UUID('735cae0d-eb08-45c3-ad79-0a11ad4dd2c2') last_activity_at=datetime.datetime(2024, 8, 23, 10, 46, 59, 10418))"]}, "execution_count": 110, "metadata": {}, "output_type": "execute_result"}], "source": ["html_template_path = \"interactive_chat.html\"\n", "\n", "settings = ex.Settings(\n", "    fields=[\n", "        ex.CustomField(name=\"chosen\", template=html_template_path, required=False),\n", "        ex.<PERSON><PERSON><PERSON><PERSON>(\n", "            name=\"rejected\",\n", "        ),\n", "    ],\n", "    questions=[\n", "        ex.RatingQuestion(\n", "            \"rating\",\n", "            title=\"How would you rate the conversation?\",\n", "            required=True,\n", "            values=[1, 2, 3, 4, 5],\n", "        ),\n", "        ex.TextQuestion(\n", "            \"improved_chosen\", title=\"Rewrite the chosen conversation\", required=True\n", "        ),\n", "    ],\n", ")\n", "\n", "dataset = ex.Dataset(\n", "    settings=settings,\n", "    name=f\"interactive_chat_{datetime.now().strftime('%Y%m%d%H%M%S')}\",\n", ")\n", "\n", "dataset.create()"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/code/argilla/argilla/src/argilla/records/_mapping/_mapper.py:89: UserWarning: Keys ['source', 'chosen_rating', 'chosen_model', 'rejected_rating', 'rejected_model', 'messages'] in data are not present in the mapping and will be ignored.\n", "  warnings.warn(f\"Keys {unknown_keys} in data are not present in the mapping and will be ignored.\")\n"]}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">DatasetRecords: The provided batch size <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">256</span> was normalized. Using value <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">100</span>.\n", "</pre>\n"], "text/plain": ["DatasetRecords: The provided batch size \u001b[1;36m256\u001b[0m was normalized. Using value \u001b[1;36m100\u001b[0m.\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Sending records...: 100%|██████████| 1/1 [00:00<00:00,  3.32batch/s]\n"]}, {"data": {"text/plain": ["DatasetRecords(Dataset(id=UUID('620fb219-73cb-42c6-bad0-456880a93ab9') inserted_at=datetime.datetime(2024, 8, 23, 10, 46, 58, 842638) updated_at=datetime.datetime(2024, 8, 23, 10, 46, 59, 10418) name='interactive_chat_20240823124658' status='ready' guidelines=None allow_extra_metadata=False distribution=OverlapTaskDistributionModel(strategy='overlap', min_submitted=1) workspace_id=UUID('735cae0d-eb08-45c3-ad79-0a11ad4dd2c2') last_activity_at=datetime.datetime(2024, 8, 23, 10, 46, 59, 10418)))"]}, "execution_count": 111, "metadata": {}, "output_type": "execute_result"}], "source": ["ds = load_dataset(\"argilla/Capybara-Preferences\", split=\"train[:100]\")\n", "ds = ds.map(lambda x: {\"messages\": x[\"chosen\"]})\n", "dataset.records.log(ds)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.19"}}, "nbformat": 4, "nbformat_minor": 2}