# Copyright 2024-present, Extralit Labs, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import uuid
from datetime import datetime

import pytest

import extralit as ex
from extralit import Extralit, Workspace
from extralit._exceptions._responses import RecordResponsesError
from extralit._exceptions._suggestions import RecordSuggestionsError


def test_add_records(client):
    mock_dataset_name = f"test_add_records{datetime.now().strftime('%Y%m%d%H%M%S')}"
    mock_data = [
        {
            "text": "Hello World, how are you?",
            "image": "http://mock.image.url/image",
            "label": "positive",
            "id": uuid.uuid4(),
        },
        {
            "text": "Hello World, how are you?",
            "image": "http://mock.image.url/image",
            "label": "negative",
            "id": uuid.uuid4(),
        },
        {
            "text": "Hello World, how are you?",
            "image": "http://mock.image.url/image",
            "label": "positive",
            "id": uuid.uuid4(),
        },
    ]
    settings = ex.Settings(
        fields=[
            ex.TextField(name="text"),
            ex.ImageField(name="image", required=True),
        ],
        questions=[
            ex.TextQuestion(name="comment", use_markdown=False),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    dataset.create()
    dataset.records.log(records=mock_data)

    dataset_records = list(dataset.records)

    assert dataset.name == mock_dataset_name
    assert dataset_records[0].id == str(mock_data[0]["id"])
    assert dataset_records[1].id == str(mock_data[1]["id"])
    assert dataset_records[2].id == str(mock_data[2]["id"])
    assert dataset_records[0].fields["text"] == mock_data[0]["text"]
    assert dataset_records[1].fields["text"] == mock_data[1]["text"]
    assert dataset_records[2].fields["text"] == mock_data[2]["text"]


def test_add_dict_records(client: Extralit, dataset_name: str):
    ws_name = "new_ws"
    ws = client.workspaces(ws_name) or Workspace(name=ws_name).create()

    ds = client.datasets("new_ds", workspace=ws)
    if ds is not None:
        ds.delete()

    ds = ex.Dataset(name=dataset_name, workspace=ws)
    ds.settings = ex.Settings(
        fields=[ex.TextField(name="text")],
        questions=[ex.TextQuestion(name="label")],
    )

    ds.create()

    mock_data = [
        {
            "text": "Hello World, how are you?",
            "label": "positive",
            "id": "1",
        },
        {
            "text": "Hello World, how are you?",
            "label": "negative",
            "id": "2",
        },
        {"text": "Hello World, how are you?", "label": "negative", "id": "3"},
    ]

    # Now the dataset is published and is ready for annotate
    ds.records.log(mock_data)

    for record, data in zip(ds.records, mock_data):
        assert record.id == data["id"]
        assert record.fields["text"] == data["text"]
        assert "label" not in record.__dict__

    for record, data in zip(ds.records(batch_size=1, with_suggestions=True), mock_data):
        assert record.id == data["id"]
        assert record.suggestions["label"].value == data["label"]


def test_add_records_with_suggestions(client) -> None:
    mock_dataset_name = f"test_add_record_with_suggestions {datetime.now().strftime('%Y%m%d%H%M%S')}"
    mock_data = [
        {
            "text": "Hello World, how are you?",
            "label": "positive",
            "id": uuid.uuid4(),
            "comment": "I'm doing great, thank you!",
            "topics": ["topic1", "topic2"],
            "topics.score": [0.9, 0.8],
        },
        {
            "text": "Hello World, how are you?",
            "label": "negative",
            "id": uuid.uuid4(),
            "comment": "I'm doing great, thank you!",
            "topics": ["topic3"],
            "topics.score": [0.9],
        },
        {
            "text": "Hello World, how are you?",
            "label": "positive",
            "id": uuid.uuid4(),
            "comment": "I'm doing great, thank you!",
            "topics": ["topic1", "topic2", "topic3"],
            "topics.score": [0.9, 0.8, 0.7],
        },
    ]
    settings = ex.Settings(
        fields=[
            ex.TextField(name="text"),
        ],
        questions=[
            ex.TextQuestion(name="comment", use_markdown=False),
            ex.MultiLabelQuestion(name="topics", labels=["topic1", "topic2", "topic3"], labels_order="suggestion"),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    dataset.create()
    dataset.records.log(
        mock_data,
        mapping={
            "comment": "comment.suggestion",
            "topics": "topics.suggestion",
            "topics.score": "topics.suggestion.score",
        },
    )
    assert dataset.name == mock_dataset_name

    dataset_records = list(dataset.records(with_suggestions=True))

    assert dataset_records[0].id == str(mock_data[0]["id"])
    assert dataset_records[0].suggestions["comment"].value == "I'm doing great, thank you!"
    assert dataset_records[0].suggestions["comment"].score is None
    assert dataset_records[0].suggestions["topics"].value == ["topic1", "topic2"]
    assert dataset_records[0].suggestions["topics"].score == [0.9, 0.8]

    assert dataset_records[1].fields["text"] == mock_data[1]["text"]
    assert dataset_records[1].suggestions["comment"].value == "I'm doing great, thank you!"
    assert dataset_records[1].suggestions["comment"].score is None
    assert dataset_records[1].suggestions["topics"].value == ["topic3"]
    assert dataset_records[1].suggestions["topics"].score == [0.9]

    assert dataset_records[2].suggestions["comment"].value == "I'm doing great, thank you!"
    assert dataset_records[2].suggestions["comment"].score is None
    assert dataset_records[2].suggestions["topics"].value == ["topic1", "topic2", "topic3"]
    assert dataset_records[2].suggestions["topics"].score == [0.9, 0.8, 0.7]


def test_add_records_with_suggestions_non_existent_question(client) -> None:
    mock_dataset_name = (
        f"test_add_record_with_suggestions_non_existent_question {datetime.now().strftime('%Y%m%d%H%M%S')}"
    )
    mock_data = [
        ex.Record(
            fields={"text": "value"}, suggestions=[ex.Suggestion(question_name="non_existent_question", value="mock")]
        )
    ]
    settings = ex.Settings(
        fields=[
            ex.TextField(name="text"),
        ],
        questions=[
            ex.TextQuestion(name="comment", use_markdown=False),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    dataset.create()
    with pytest.raises(RecordSuggestionsError, match="Extralit SDK error: RecordSuggestionsError: Record suggestion"):
        dataset.records.log(mock_data)


def test_add_records_with_responses(client, username: str) -> None:
    mock_dataset_name = f"test_modify_record_responses_locally {uuid.uuid4()}"
    mock_data = [
        {
            "text": "Hello World, how are you?",
            "my_label": "positive",
            "id": uuid.uuid4(),
        },
        {
            "text": "Hello World, how are you?",
            "my_label": "positive",
            "id": uuid.uuid4(),
        },
        {
            "text": "Hello World, how are you?",
            "my_label": "negative",
            "id": uuid.uuid4(),
        },
    ]
    settings = ex.Settings(
        fields=[
            ex.TextField(name="text"),
        ],
        questions=[
            ex.LabelQuestion(name="label", labels=["positive", "negative"]),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    user = ex.User(
        username=username,
        first_name="test",
        password="testtesttest",
        client=client,
    )
    user.create()
    dataset.create()
    dataset.records.log(
        records=mock_data,
        user_id=user.id,
        mapping={
            "my_label": "label.response",
        },
    )
    assert dataset.name == mock_dataset_name

    dataset_records = list(dataset.records(with_responses=True))

    for record, mock_record in zip(dataset_records, mock_data):
        assert record.id == str(mock_record["id"])
        assert record.fields["text"] == mock_record["text"]
        assert record.responses["label"][0].value == mock_record["my_label"]
        assert record.responses["label"][0].user_id == user.id


def test_add_records_with_responses_non_existent_question(client, username: str) -> None:
    mock_dataset_name = (
        f"test_add_record_with_responses_non_existent_question {datetime.now().strftime('%Y%m%d%H%M%S')}"
    )

    settings = ex.Settings(
        fields=[
            ex.TextField(name="text"),
        ],
        questions=[
            ex.TextQuestion(name="comment", use_markdown=False),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    dataset.create()
    user = ex.User(
        username=username,
        first_name="test",
        password="testtesttest",
        client=client,
    )
    user.create()
    mock_data = [
        ex.Record(
            fields={"text": "value"},
            responses=[ex.Response(question_name="non_existent_question", value="mock", user_id=user.id)],
        )
    ]
    with pytest.raises(RecordResponsesError, match="Extralit SDK error: RecordResponsesError: Record response"):
        dataset.records.log(mock_data)


def test_add_records_with_responses_and_suggestions(client, username: str) -> None:
    mock_dataset_name = f"test_modify_record_responses_locally {uuid.uuid4()}"
    mock_data = [
        {
            "text": "Hello World, how are you?",
            "my_label": "negative",
            "my_guess": "positive",
            "id": uuid.uuid4(),
        },
        {
            "text": "Hello World, how are you?",
            "my_label": "negative",
            "my_guess": "positive",
            "id": uuid.uuid4(),
        },
        {
            "text": "Hello World, how are you?",
            "my_label": "negative",
            "my_guess": "positive",
            "id": uuid.uuid4(),
        },
    ]
    settings = ex.Settings(
        fields=[ex.TextField(name="text")],
        questions=[
            ex.LabelQuestion(name="label", labels=["positive", "negative"]),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    user = ex.User(
        username=username,
        first_name="test",
        password="testtesttest",
        client=client,
    )
    user.create()
    dataset.create()
    dataset.records.log(
        records=mock_data,
        user_id=user.id,
        mapping={
            "my_label": "label.response",
            "my_guess": "label.suggestion",
        },
    )
    assert dataset.name == mock_dataset_name

    dataset_records = list(dataset.records(with_suggestions=True))

    assert dataset_records[0].id == str(mock_data[0]["id"])
    assert dataset_records[1].fields["text"] == mock_data[1]["text"]
    assert dataset_records[2].suggestions["label"].value == "positive"
    assert dataset_records[2].responses["label"][0].value == "negative"
    assert dataset_records[2].responses["label"][0].user_id == user.id


def test_add_records_with_fields_mapped(client, username: str) -> None:
    mock_dataset_name = f"test_modify_record_responses_locally {uuid.uuid4()}"
    mock_data = [
        {
            "x": "Hello World, how are you?",
            "my_label": "negative",
            "my_guess": "positive",
            "id": uuid.uuid4(),
            "score": 0.5,
        },
        {
            "x": "Hello World, how are you?",
            "my_label": "negative",
            "my_guess": "positive",
            "id": uuid.uuid4(),
            "score": 0.5,
        },
        {
            "x": "Hello World, how are you?",
            "my_label": "negative",
            "my_guess": "positive",
            "id": uuid.uuid4(),
            "score": 0.5,
        },
    ]
    settings = ex.Settings(
        fields=[
            ex.TextField(name="text"),
        ],
        questions=[
            ex.LabelQuestion(name="label", labels=["positive", "negative"]),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    user = ex.User(
        username=username,
        first_name="test",
        password="testtesttest",
        client=client,
    )
    user.create()
    dataset.create()
    dataset.records.log(
        records=mock_data,
        user_id=user.id,
        mapping={
            "my_label": "label.response",
            "my_guess": "label.suggestion",
            "x": "text",
            "score": "label.suggestion.score",
        },
    )
    assert dataset.name == mock_dataset_name

    dataset_records = list(dataset.records(with_suggestions=True))

    assert dataset_records[0].id == str(mock_data[0]["id"])
    assert dataset_records[1].fields["text"] == mock_data[1]["x"]
    assert dataset_records[2].suggestions["label"].value == "positive"
    assert dataset_records[2].suggestions["label"].score == 0.5
    assert dataset_records[2].responses["label"][0].value == "negative"
    assert dataset_records[2].responses["label"][0].value == "negative"
    assert dataset_records[2].responses["label"][0].user_id == user.id


def test_add_records_with_id_mapped(client, username: str) -> None:
    mock_dataset_name = f"test_modify_record_responses_locally {uuid.uuid4()}"
    mock_data = [
        {
            "x": "Hello World, how are you?",
            "my_label": "negative",
            "my_guess": "positive",
            "uuid": uuid.uuid4(),
        },
        {
            "x": "Hello World, how are you?",
            "my_label": "negative",
            "my_guess": "positive",
            "uuid": uuid.uuid4(),
        },
        {
            "x": "Hello World, how are you?",
            "my_label": "negative",
            "my_guess": "positive",
            "uuid": uuid.uuid4(),
        },
    ]
    settings = ex.Settings(
        fields=[
            ex.TextField(name="text"),
        ],
        questions=[
            ex.LabelQuestion(name="label", labels=["positive", "negative"]),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    user = ex.User(
        username=username,
        first_name="test",
        password="testtesttest",
        client=client,
    )
    user.create()
    dataset.create()
    dataset.records.log(
        records=mock_data,
        user_id=user.id,
        mapping={"my_label": "label.response", "my_guess": "label.suggestion", "x": "text", "uuid": "id"},
    )
    assert dataset.name == mock_dataset_name

    dataset_records = list(dataset.records(with_suggestions=True))

    assert dataset_records[0].id == str(mock_data[0]["uuid"])
    assert dataset_records[1].fields["text"] == mock_data[1]["x"]
    assert dataset_records[2].suggestions["label"].value == "positive"
    assert dataset_records[2].responses["label"][0].value == "negative"
    assert dataset_records[2].responses["label"][0].user_id == user.id


def test_add_record_resources(client):
    user_id = client.users[0].id
    mock_dataset_name = f"test_add_records{datetime.now().strftime('%Y%m%d%H%M%S')}"
    mock_resources = [
        ex.Record(
            fields={"text": "Hello World, how are you?"},
            suggestions=[
                ex.Suggestion("label", "positive", score=0.9),
                ex.Suggestion("topics", ["topic1", "topic2"], score=[0.9, 0.8]),
            ],
            responses=[ex.Response("label", "positive", user_id=user_id)],
            id=str(uuid.uuid4()),
        ),
        ex.Record(
            fields={"text": "Hello World, how are you?"},
            suggestions=[
                ex.Suggestion("label", "positive", score=0.9),
                ex.Suggestion("topics", ["topic1", "topic2"], score=[0.9, 0.8]),
            ],
            responses=[ex.Response("label", "positive", user_id=user_id)],
            id=str(uuid.uuid4()),
        ),
        ex.Record(
            fields={"text": "Hello World, how are you?"},
            suggestions=[
                ex.Suggestion("label", "positive", score=0.9),
                ex.Suggestion("topics", ["topic1", "topic2"], score=[0.9, 0.8]),
            ],
            responses=[ex.Response("label", "positive", user_id=user_id)],
            id=str(uuid.uuid4()),
        ),
    ]
    settings = ex.Settings(
        fields=[
            ex.TextField(name="text"),
        ],
        questions=[
            ex.LabelQuestion(name="label", labels=["positive", "negative"]),
            ex.MultiLabelQuestion(name="topics", labels=["topic1", "topic2", "topic3"]),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    dataset.create()
    dataset.records.log(records=mock_resources)

    dataset_records = list(dataset.records(with_suggestions=True))

    assert dataset.name == mock_dataset_name

    assert dataset_records[0].id == str(mock_resources[0].id)
    assert dataset_records[0].suggestions["label"].value == "positive"
    assert dataset_records[0].suggestions["label"].score == 0.9
    assert dataset_records[0].suggestions["topics"].value == ["topic1", "topic2"]
    assert dataset_records[0].suggestions["topics"].score == [0.9, 0.8]

    assert dataset_records[1].id == str(mock_resources[1].id)
    assert dataset_records[1].suggestions["label"].value == "positive"
    assert dataset_records[1].suggestions["label"].score == 0.9
    assert dataset_records[1].suggestions["topics"].value == ["topic1", "topic2"]
    assert dataset_records[1].suggestions["topics"].score == [0.9, 0.8]

    assert dataset_records[2].id == str(mock_resources[2].id)
    assert dataset_records[2].suggestions["label"].value == "positive"
    assert dataset_records[2].suggestions["label"].score == 0.9
    assert dataset_records[2].suggestions["topics"].value == ["topic1", "topic2"]
    assert dataset_records[2].suggestions["topics"].score == [0.9, 0.8]


def test_add_record_with_chat_field(client: ex.Extralit, dataset_name: str):
    mock_resources = [
        ex.Record(
            fields={
                "chat": [
                    {
                        "role": "user",
                        "content": "Hello World, how are you?",
                    },
                    {
                        "role": "bot",
                        "content": "I'm doing great, thank you!",
                    },
                ]
            },
        ),
        ex.Record(
            fields={
                "chat": [
                    {
                        "role": "user",
                        "content": "Hello World, how are you?",
                    },
                    {
                        "role": "bot",
                        "content": "I'm doing great, thank you!",
                    },
                ]
            },
        ),
    ]
    settings = ex.Settings(
        fields=[
            ex.ChatField(name="chat", required=False),
        ],
        questions=[
            ex.TextQuestion(name="comment", use_markdown=False),
        ],
    )
    dataset = ex.Dataset(
        name=dataset_name,
        settings=settings,
        client=client,
    )
    dataset.create()
    dataset.records.log(records=mock_resources)
    list(dataset.records)

    assert dataset.name == dataset_name


def test_add_records_with_optional_chat_field(client: ex.Extralit, dataset_name: str):
    mock_resources = [
        ex.Record(
            fields={
                "text": "This a text",
                "chat": None,
            },
        ),
        ex.Record(
            fields={
                "text": "This a text",
            },
        ),
    ]
    settings = ex.Settings(
        fields=[
            ex.TextField(name="text", required=True),
            ex.ChatField(name="chat", required=False),
        ],
        questions=[
            ex.TextQuestion(name="comment", use_markdown=False),
        ],
    )
    dataset = ex.Dataset(
        name=dataset_name,
        settings=settings,
        client=client,
    )
    dataset.create()
    dataset.records.log(records=mock_resources)
    assert len(list(dataset.records(query="this"))) == 2  # Forcing search to check the records indexation

    assert dataset.name == dataset_name


def test_add_records_with_responses_and_same_schema_name(client: Extralit, username: str):
    mock_dataset_name = f"test_modify_record_responses_locally {uuid.uuid4()}"
    mock_data = [
        {
            "text": "Hello World, how are you?",
            "label": "negative",
        },
        {
            "text": "Hello World, how are you?",
            "label": "negative",
        },
        {
            "text": "Hello World, how are you?",
            "label": "negative",
        },
    ]
    settings = ex.Settings(
        fields=[
            ex.TextField(name="text"),
        ],
        questions=[
            ex.LabelQuestion(name="label", labels=["positive", "negative"]),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    user = ex.User(
        username=username,
        first_name="test",
        password="testtesttest",
        client=client,
    )
    user.create()
    dataset.create()
    dataset.records.log(
        records=mock_data,
        user_id=user.id,
        mapping={"label": "label.response", "text": "text"},
    )
    assert dataset.name == mock_dataset_name

    dataset_records = list(dataset.records(with_responses=True))

    assert dataset_records[0].fields["text"] == mock_data[1]["text"]
    assert dataset_records[1].responses["label"][0].value == "negative"
    assert dataset_records[1].responses["label"][0].user_id == user.id


def test_add_records_objects_with_responses(client: Extralit, username: str):
    mock_dataset_name = f"test_modify_record_responses_locally {uuid.uuid4()}"

    settings = ex.Settings(
        fields=[
            ex.TextField(name="text"),
        ],
        questions=[
            ex.LabelQuestion(name="label", labels=["positive", "negative"]),
            ex.TextQuestion(name="comment", use_markdown=False, required=False),
        ],
    )
    dataset = ex.Dataset(
        name=mock_dataset_name,
        settings=settings,
        client=client,
    )
    user = ex.User(
        username=username,
        first_name="test",
        password="testtesttest",
        client=client,
    )
    user.create()
    dataset.create()

    records = [
        ex.Record(
            fields={"text": "Hello World, how are you?"},
            responses=[ex.Response("label", "negative", user_id=user.id, status="submitted")],
            id=str(uuid.uuid4()),
        ),
        ex.Record(
            fields={"text": "Hello World, how are you?"},
            responses=[ex.Response("label", "positive", user_id=user.id, status="discarded")],
            id=str(uuid.uuid4()),
        ),
        ex.Record(
            fields={"text": "Hello World, how are you?"},
            responses=[ex.Response("comment", "The comment", user_id=user.id, status="draft")],
            id=str(uuid.uuid4()),
        ),
        ex.Record(
            fields={"text": "Hello World, how are you?"},
            responses=[ex.Response("comment", "The comment", user_id=user.id)],
            id=str(uuid.uuid4()),
        ),
    ]

    dataset.records.log(records)

    dataset_records = list(dataset.records())

    assert dataset.name == mock_dataset_name
    assert dataset_records[0].id == records[0].id
    assert dataset_records[0].responses["label"][0].value == "negative"
    assert dataset_records[0].responses["label"][0].status == "submitted"

    assert dataset_records[1].id == records[1].id
    assert dataset_records[1].responses["label"][0].value == "positive"
    assert dataset_records[1].responses["label"][0].status == "discarded"

    assert dataset_records[2].id == records[2].id
    assert dataset_records[2].responses["comment"][0].value == "The comment"
    assert dataset_records[2].responses["comment"][0].status == "draft"

    assert dataset_records[3].id == records[3].id
    assert dataset_records[3].responses["comment"][0].value == "The comment"
    assert dataset_records[3].responses["comment"][0].status == "draft"


def test_add_records_with_boolean_metadata(client: Extralit, dataset_name: str):
    settings = ex.Settings(
        fields=[ex.TextField(name="text")],
        metadata=[ex.TermsMetadataProperty(name="boolean", options=[True, False])],
        questions=[ex.TextQuestion(name="comment", use_markdown=False)],
    )
    dataset = ex.Dataset(
        name=dataset_name,
        settings=settings,
        client=client,
    ).create()

    dataset.records.log(
        [
            {"id": 0, "text": "Hello World, how are you?", "boolean": True},
            {"id": 1, "text": "Hello World, how are you?", "boolean": False},
            {"id": 2, "text": "Hello World, how are you?"},
        ]
    )

    dataset_records = list(dataset.records())
    assert dataset_records[0].metadata["boolean"] is True
    assert dataset_records[1].metadata["boolean"] is False
    assert "boolean" not in dataset_records[2].metadata
