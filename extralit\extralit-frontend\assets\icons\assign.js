/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'assign': {
    width: 10,
    height: 10,
    viewBox: '0 0 10 10',
    data: '<path pid="0" d="M1.625 2.75c.416 0 .78-.226.974-.563h3.526a1.125 1.125 0 010 2.25h-2.25a2.25 2.25 0 000 4.5h3.526a1.125 1.125 0 100-1.124H3.875a1.125 1.125 0 010-2.25h2.25a2.25 2.25 0 000-4.5H2.599a1.125 1.125 0 10-.974 1.687z"/>'
  }
})