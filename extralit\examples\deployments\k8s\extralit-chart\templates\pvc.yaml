{{- if .Values.extralit.persistence.enabled -}}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "extralit.fullname" . }}-pvc
  labels:
    {{- include "extralit.labels" . | nindent 4 }}
spec:
  accessModes:
    - {{ .Values.extralit.persistence.accessMode | quote }}
  resources:
    requests:
      storage: {{ .Values.extralit.persistence.size | quote }}
  {{- if .Values.extralit.persistence.storageClass }}
  storageClassName: {{ .Values.extralit.persistence.storageClass | quote }}
  {{- end }}
{{- end }}