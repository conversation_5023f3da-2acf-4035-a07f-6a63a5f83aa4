apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "extralit.fullname" . }}-worker
  labels:
    {{- include "worker.labels" . | nindent 4 }}
    app.kubernetes.io/component: worker
spec:
  replicas: {{ .Values.worker.replicaCount | default 1 }}
  selector:
    matchLabels:
      {{- include "worker.selectorLabels" . | nindent 6 }}
      app.kubernetes.io/component: worker
  template:
    metadata:
      labels:
        {{- include "worker.selectorLabels" . | nindent 8 }}
        app.kubernetes.io/component: worker
    spec:
      containers:
        - name: {{ .Chart.Name }}-worker
          image: "{{ .Values.extralit.image.repository }}:{{ .Values.extralit.image.tag }}"
          env:
            - name: EXTRALIT_ELASTICSEARCH
              value: {{if .Values.elasticsearch.useOperator}}"http://{{ include "extralit.fullname" . }}-es-http:9200"{{else}}"{{ .Values.externalElasticsearch.host }}:{{ .Values.externalElasticsearch.port }}"{{end}}
            - name: EXTRALIT_REDIS_URL
              value: redis://{{ .Release.Name }}-redis-master:6379/0
            - name: BACKGROUND_NUM_WORKERS
              value: "{{ .Values.worker.numWorkers | default 2 }}"
            {{- if .Values.extralit.persistence.enabled }}
            - name: EXTRALIT_HOME_PATH
              value: {{ .Values.extralit.persistence.mountPath | quote }}
            {{- end }}
          command: ["sh", "-c", "python -m extralit_server worker --num-workers ${BACKGROUND_NUM_WORKERS}"]
          {{- if .Values.extralit.persistence.enabled }}
          volumeMounts:
            - name: extralit-data
              mountPath: {{ .Values.extralit.persistence.mountPath}}
          {{- end }}
          resources:
            {{- toYaml .Values.worker.resources | nindent 12 }}
      {{- if .Values.extralit.persistence.enabled }}
      volumes:
        - name: extralit-data
          persistentVolumeClaim:
            claimName: {{ .Release.Name }}-pvc
      {{- end }}