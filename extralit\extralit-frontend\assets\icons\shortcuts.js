/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'shortcuts': {
    width: 16,
    height: 16,
    viewBox: '0 0 32 32',
    data: '<path pid="0" d="M7 19h3v-6H7a6 6 0 116-6v3h6V7a6 6 0 116 6h-3v6h3a6 6 0 11-6 6v-3h-6v3a6 6 0 11-6-6zM7 4a3 3 0 000 6h3V7a3 3 0 00-3-3zm21 3a3 3 0 00-6 0v3h3a3 3 0 003-3zm-3 21a3 3 0 000-6h-3v3a3 3 0 003 3zm-6-9v-6h-6v6h6zM7 28a3 3 0 003-3v-3H7a3 3 0 000 6z" _fill="#000" _stroke="#000" stroke-width=".008"/>'
  }
})