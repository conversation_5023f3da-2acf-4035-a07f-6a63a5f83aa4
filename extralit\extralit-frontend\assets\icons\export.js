/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'export': {
    width: 26,
    height: 31,
    viewBox: '0 0 26 31',
    data: '<path pid="0" d="M19.852 7.734L17.92 9.666l-3.463-3.463V22.86h-2.732V6.203L8.262 9.666 6.33 7.734l6.761-6.76 6.76 6.76z" _fill="#000" fill-opacity=".87"/><path pid="1" d="M3.53 28.242V14.583h5.463v-2.732H.798v19.123h24.586V11.85h-8.195v2.732h5.463v13.659H3.53z" _fill="#000" fill-opacity=".87"/>'
  }
})