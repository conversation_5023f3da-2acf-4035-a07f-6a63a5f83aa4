/*!
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Fonts
//-----------
$primary-font-family: "Inter", "Helvetica", "Arial", sans-serif;
$secondary-font-family: "raptor_v2_premiumbold", "Helvetica", "Arial",
  sans-serif;
$tertiary-font-family: "Roboto Condensed", sans-serif;
$quaternary-font-family: "Roboto Mono", monospace;
$base-font-size: 14px;
$base-line-height: 1.4em;

// Grid
//-----------
$maxwidth: 980px;

// Spaces
//-----------
$base-space: 8px;
$base-space-between-records: $base-space;

// Border radius
//---------------
$border-radius-s: 5px;
$border-radius: 5px;
$border-radius-m: 10px;
$border-radius-l: 16px;
$border-radius-xl: 25px;
$border-radius-rounded: 50em;

// Shadows
//-----------
$shadow: 0 8px 20px 0 rgba(0, 0, 0, 0.2);
$shadow-100: 1px 1px 3px 0px rgba(0, 0, 0, 0.15);
$shadow-200: 1px 2px 5px 2px rgba(0, 0, 0, 0.15);

// Input size
//-----------
$input-size: 40px;

// Buttons
//-----------
$button-height: 30px;
$button-radius: 6px;

// Transitions
//-----------
$swift-ease-out-duration: 0.3s !default;
$swift-ease-out-timing-function: cubic-bezier(0.25, 0.8, 0.25, 1) !default;
$swift-ease-out: all $swift-ease-out-duration $swift-ease-out-timing-function !default;
$swift-ease-in-duration: 0.3s !default;
$swift-ease-in-timing-function: cubic-bezier(0.55, 0, 0.55, 0.2) !default;
$swift-ease-in: all $swift-ease-in-duration $swift-ease-in-timing-function !default;
$swift-ease-in-out-duration: 0.5s !default;
$swift-ease-in-out-timing-function: cubic-bezier(0.35, 0, 0.25, 1) !default;
$swift-ease-in-out: all $swift-ease-in-out-duration;
$swift-linear-duration: 0.15s !default;
$swift-linear-timing-function: linear !default;
$swift-linear: all $swift-linear-duration $swift-linear-timing-function !default;

// topbar
$topbarHeight: $base-space * 7;

// question-form
$questionFormWidth: 40vw;

// sidebar
$sidebarPanelWidth: 280px;
$sidebarMenuWidth: 70px;
$sidebarWidth: $sidebarPanelWidth + $sidebarMenuWidth;

// sidebar pdf viewer
$sidebarPdfViewerWidth: 40vw;

:root {
  --sidebar-width: #{$sidebarPdfViewerWidth};
  --questions-form-width: #{$questionFormWidth};
}