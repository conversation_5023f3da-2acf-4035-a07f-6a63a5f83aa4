---
description: These are the tools and resources to be up-to-date with the Extralit development and contribute to the project.
hide:
    - toc
    - footer
---

# Community

We are an open-source community-driven project focused on building a platform that accelerates scientific data extraction while fostering a collaborative community. We would love to hear from you and help you get started with Extralit!

<div class="grid cards" markdown>

-   __Contributor Guide__

    ---

    Learn how to contribute to Extralit's projects with our contributor documentation.

    [:octicons-arrow-right-24: Contributor Guide ↗](contributor.md)

-   __Developer Guide__

    ---

    Learn how to set up Extralit's development  with our comprehensive developer documentation.

    [:octicons-arrow-right-24: Developer Guide ↗](developer.md)

-   __Slack__

    ---

    Join our Slack workspace to get direct support from the community and stay up to date with the latest developments.

    [:octicons-arrow-right-24: Slack ↗](https://join.slack.com/t/extralit/shared_invite/zt-2kt8t12r7-uFj0bZ5SPAOhRFkxP7ZQaQ)

<!-- -   __Blog__

    ---

    Follow our blog for technical deep-dives, user stories, and project updates.

    [:octicons-arrow-right-24: Blog ↗](https://extralit.ai/blog) -->

-   __Changelog__

    ---

    The changelog is where you can find the latest updates and changes to the Extralit project.

    [:octicons-arrow-right-24: Changelog ↗](https://github.com/extralit/extralit/blob/develop/extralit/CHANGELOG.md)

-   __Roadmap__

    ---

    We love to discuss our plans with the community. Feel encouraged to participate in our roadmap discussions.

    [:octicons-arrow-right-24: Roadmap ↗](https://github.com/orgs/extralit/projects/2/views/1)


</div>

## Getting Started

- Check out our [documentation](https://docs.extralit.ai) to learn more about Extralit
- Join our [Slack community](https://join.slack.com/t/extralit/shared_invite/zt-2kt8t12r7-uFj0bZ5SPAOhRFkxP7ZQaQ) to connect with other users and contributors
- Read our [contribution guide](contributor.md) to start contributing to the project
