/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'focus-mode': {
    width: 20,
    height: 16,
    viewBox: '0 0 20 16',
    data: '<path pid="0" clip-rule="evenodd" d="M0 .889C0 .398.373 0 .833 0h18.334c.46 0 .833.398.833.889V15.11c0 .491-.373.889-.833.889H.833C.373 16 0 15.602 0 15.111V.89zm1.667.889v12.444h16.666V1.778H1.667z"/>'
  }
})