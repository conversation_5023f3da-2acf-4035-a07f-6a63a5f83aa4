import os
import redis
from rq import Worker, Queue, Connection

# Pre-import heavy libs (PyMuPDF imported by tasks module)
from agpl_worker.jobs import pdf_tasks  # noqa: F401

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
QUEUES = os.getenv("RQ_QUEUES", "pdf").split(",")

def main():
    conn = redis.from_url(REDIS_URL)
    with Connection(conn):
        # On Linux, default Worker uses fork-per-job for good memory sharing (COW)
        w = Worker([Queue(name) for name in QUEUES])
        w.work(logging_level="INFO")

if __name__ == "__main__":
    main()

