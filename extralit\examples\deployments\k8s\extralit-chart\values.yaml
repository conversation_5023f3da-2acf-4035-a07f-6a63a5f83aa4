extralit:
  replicaCount: 1
  image:
    repository: extralit/extralit-server
    tag: latest
  resources:
    requests:
      cpu: "0.5"
    limits:
      cpu: "1"
  authSecretKey: "CHANGE_ME"
  auth:
    username: "extralit"
    password: "12345678"
    apiKey: "extralit.apikey"
  persistence:
    enabled: true
    accessMode: ReadWriteOnce
    size: 2Gi
    mountPath: "/data"
  ingress:
    enabled: true
    className: "nginx"
    host: "extralit.local"
    annotations:
      kubernetes.io/ingress.class: nginx
      nginx.ingress.kubernetes.io/ssl-redirect: "false"
      nginx.ingress.kubernetes.io/use-regex: "true"
  configmap:
    minikubeIP: "************"
  hpa:
    enabled: true
    minReplicas: 1
    maxReplicas: 3
    targetCPUUtilizationPercentage: 50

# Elasticsearch installation is enabled by default.
# To disable it, set `elasticsearch.useOperator` to `false`
# and set `externalElasticsearch.host` to the host of your existing Elasticsearch instance.
elasticsearch:
  sslVerify: true
  useOperator: true
  version: 8.5.3
  nodeCount: 1
  disableAuthentication: true
  resources:
    requests:
      cpu: 0.25
      memory: 1Gi
    limits:
      cpu: 1
      memory: 2Gi
  persistence:
    enabled: true
    storage:
      size: 1Gi
      accessModes:
        - ReadWriteOnce

externalElasticsearch:
  host: "extralit.local"
  port: 9200
  path: "/es"

redis:
  enabled: true
  architecture: standalone
  auth:
    enabled: false
  master:
    persistence:
      enabled: true
      accessModes:
        - ReadWriteOnce
      size: 1Gi
      resources:
        requests:
          cpu: 0.25
          memory: 1Gi
        limits:
          cpu: 1
          memory: 2Gi

externalRedis:
  enabled: false
  url: "redis://localhost:6379/0"
  is_redis_cluster: false

serviceAccount:
  create: false

worker:
  replicaCount: 1
  numWorkers: 2
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
