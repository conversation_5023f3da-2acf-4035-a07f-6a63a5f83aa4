apiVersion: batch/v1
kind: CronJob
metadata:
  name: purge-evicted-pods
spec:
  schedule: "*/5 * * * *"  # Runs every 5 minutes
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: kubectl
            image: bitnami/kubectl:latest
            command:
            - /bin/sh
            - -c
            - kubectl delete pod --field-selector=status.phase==Failed
          restartPolicy: OnFailure