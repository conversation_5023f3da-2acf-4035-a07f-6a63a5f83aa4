<!--
PR Title format: [Type] Short description
Examples:
- [Fix] Resolve authentication bug
- [Feature] Add dark mode support
- [Refactor] Improve user profile component
- [Docs] Update API documentation
- [Chore] Update dependencies
-->

## Description

<!--
Please do not leave this blank
This PR [adds/removes/fixes/replaces] the [feature/bug/etc].
-->

## Related Tickets & Documents

Closes #<!-- Add issue number here -->

## What type of PR is this? (check all applicable)

- [ ] Refactor
- [ ] Feature
- [ ] Bug Fix
- [ ] Optimization
- [ ] Documentation Update

## Steps to QA
<!--
Please provide some steps for the reviewer to test your change. If you have wrote tests, you can mention that here instead.

1. Click a link
2. Do this thing
3. Validate you see the thing working
-->

## Added/updated tests?

- [ ] Yes
- [ ] No, and this is why: _please replace this line with details on why tests
      have not been included_
- [ ] I need help with writing tests

## Added/updated documentations?

- [ ] Yes
- [ ] No, and this is why: _please replace this line with details on why tests
      have not been included_
- [ ] I need help with writing docs

## Checklist
- [ ] I have added relevant notes to the CHANGELOG.md file (See https://keepachangelog.com/)

