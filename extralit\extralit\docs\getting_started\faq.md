---
description: Frequently Asked Questions about Extralit for scientific data extraction.
hide: toc
---

# Extralit FAQ

??? Question "What is Extralit?"

    Extralit is a tool that helps you extract structured data from scientific papers. It uses advanced AI models to make the process faster and more accurate, so you can focus on your research instead of manual data collection.

??? Question "Who should use Extralit?"

    Extralit is designed for scientists, research assistants, and anyone who needs to collect data from academic literature. If you need to build datasets from research papers for analysis, modeling or review, Extralit can help.

??? Question "Do I need to know programming well to use Extralit?"

    No. Extralit provides an easy-to-use web interface. You do not need to write code or have strong data skills to extract data. The tool guides you step by step.

??? Question "What kind of data can Extralit extract?"

    Extralit can extract many types of information, such as tables, results, study characteristics, and other details from research papers. You can define what data you want to collect using simple templates. It can process research papers from PDFs directly, even scanned PDFs, extracting content from complex tables and text.

??? Question "Can Extralit handle non-English papers?"

    Yes, Extralit supports many languages. You can extract data from papers written in different languages, depending on the AI model used.

??? Question "How does <PERSON>lit ensure data quality?"

    Extralit uses AI to suggest data extractions, but you can review and correct the results. This human-in-the-loop approach helps ensure the extracted data is accurate and reliable. The quality of extracted data also depends significantly on how well you define your schema - a clear, specific schema will yield better results than a vague one.

??? Question "Is my data safe with Extralit?"

    Yes. Extralit can be run on your own server, so your data stays private. You control where your data is stored.

??? Question "Do I need to install anything to use Extralit?"

    You can use Extralit through a web browser. There is also a public demo available online. For more control, you can install Extralit on your own cloud provider (e.g. Huggingface Spaces).

??? Question "How much does Extralit cost?"

    Extralit is open-source and free to use. However, if you use a cloud provider, there may be costs associated with hosting and running the application. Additional costs can incur on API calls to LLM models and to PDF OCR pipelines.

??? Question "Where can I get help or support?"

    You can find guides and documentation on the Extralit website. There is also a Slack community where you can ask questions and get help from other users.