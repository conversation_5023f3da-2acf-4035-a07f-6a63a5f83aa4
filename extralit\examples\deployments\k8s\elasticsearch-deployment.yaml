apiVersion: apps/v1
kind: Deployment
metadata:
  name: elasticsearch
  labels:
    app: elasticsearch
spec:
  selector:
    matchLabels:
      app: elasticsearch
  replicas: 1
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 1
            preference:
              matchExpressions:
              - key: role
                operator: In
                values:
                - backend
      containers:
        - name: elasticsearch
          image: docker.elastic.co/elasticsearch/elasticsearch:8.17.0
          resources:
            requests:
              cpu: 0.25
              memory: 1Gi
            limits:
              cpu: 1
              memory: 2Gi
          ports:
            - containerPort: 9200
          env:
            - name: discovery.type
              value: single-node
            - name: ES_JAVA_OPTS
              value: -Xms256m -Xmx256m
            - name: cluster.routing.allocation.disk.threshold_enabled
              value: "false"
            - name: xpack.security.enabled
              value: "false"
            - name: xpack.security.transport.ssl.enabled
              value: "false"
            - name: xpack.security.http.ssl.enabled
              value: "false"
          volumeMounts:
            - mountPath: /usr/share/elasticsearch/data
              name: elasticsearch-data
      volumes:
        - name: elasticsearch-data
          persistentVolumeClaim:
            claimName: elasticsearch-pvc
