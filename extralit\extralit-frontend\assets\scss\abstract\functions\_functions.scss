/*!
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Functions
// -----------

// http://zerosixthree.se/8-sass-mixins-you-must-have-in-your-toolbox/
//
//  Set a rem font size with pixel fallback
//
//  --- USAGE:
//
//  p {
//    @include font-size(14px)
//  }

@function calculateRem($size) {
  $remSize: calc($size / 16px);
  @return $remSize * 1rem;
}
