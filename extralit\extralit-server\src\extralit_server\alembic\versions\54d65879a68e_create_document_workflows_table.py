# Copyright 2024-present, Extralit Labs, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""create_document_workflows_table

Revision ID: 54d65879a68e
Revises: 7d6b33203390
Create Date: 2025-08-17 23:12:00.379621

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "54d65879a68e"
down_revision = "7d6b33203390"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "workflows",
        sa.Column("workflow_type", sa.String(length=50), nullable=False),
        sa.Column("status", sa.String(length=50), default="pending", nullable=False),
        sa.Column("workspace_id", sa.Uuid(), nullable=False),
        sa.Column("document_id", sa.Uuid(), nullable=False),
        sa.Column("reference", sa.String(length=255), nullable=True),
        sa.Column("group_id", sa.String(length=255), nullable=False),
        sa.Column("id", sa.Uuid(), nullable=False),
        sa.Column("inserted_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(["document_id"], ["documents.id"], ondelete="CASCADE"),
        sa.ForeignKeyConstraint(["workspace_id"], ["workspaces.id"]),
        sa.PrimaryKeyConstraint("id"),
    )
    op.create_index(op.f("ix_workflows_document_id"), "workflows", ["document_id"], unique=False)
    op.create_index(op.f("ix_workflows_reference"), "workflows", ["reference"], unique=False)
    op.create_index(op.f("ix_workflows_group_id"), "workflows", ["group_id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_workflows_group_id"), table_name="workflows")
    op.drop_index(op.f("ix_workflows_reference"), table_name="workflows")
    op.drop_index(op.f("ix_workflows_document_id"), table_name="workflows")
    op.drop_table("workflows")
    # ### end Alembic commands ###
