from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Optional, List, Union
from rq.job import Job
from app.rq_conn import pdf_queue

router = APIRouter(prefix="/pdf", tags=["pdf"])

class ExtractTextRequest(BaseModel):
    input_uri: str = Field(..., description="Signed https URL or s3://bucket/key")
    pages: Optional[Union[List[int], str]] = None
    output_bucket: Optional[str] = None
    output_key: Optional[str] = None

@router.post("/extract")
def enqueue_extract(req: ExtractTextRequest):
    # NOTE: Do NOT import AGPL code here. Just pass the task name string and args.
    job = pdf_queue.enqueue(
        "agpl_worker.jobs.pdf_tasks.extract_text_from_pdf",
        kwargs=req.model_dump(),
        job_timeout=600,
        retry=None,
        result_ttl=3600,
        failure_ttl=86400,
        description=f"extract_text:{req.input_uri}",
    )
    return {"job_id": job.get_id(), "status": job.get_status()}

@router.get("/status/{job_id}")
def job_status(job_id: str):
    job = Job.fetch(job_id, connection=pdf_queue.connection)
    resp = {
        "job_id": job_id,
        "status": job.get_status(),
        "enqueued_at": job.enqueued_at,
        "started_at": job.started_at,
        "ended_at": job.ended_at,
    }
    if job.is_failed:
        resp["error"] = str(job.exc_info or "")
    if job.is_finished:
        # Beware of large payloads in Redis; prefer returning a URI from the worker
        resp["result"] = job.result
    return resp