---
description: These are the tutorials for the Extralit SDK. They provide step-by-step instructions for common tasks.
hide: toc
---


# Tutorials

These are the tutorials for *the Extralit SDK*. They provide step-by-step instructions for common tasks.

<div class="grid cards" markdown>

-   __Text classification__

    ---

    Learn about a standard workflow for a text classification task with model fine-tuning.

    [:octicons-arrow-right-24: Tutorial](text_classification.ipynb)

-   __Token classification__

    ---

    Learn about a standard workflow for a token classification task with model fine-tuning.

    [:octicons-arrow-right-24: Tutorial](token_classification.ipynb)


-   __Image classification__

    ---

    Learn about a standard workflow for an image classification task with model fine-tuning.

    [:octicons-arrow-right-24: Tutorial](image_classification.ipynb)

-   __Image preference__

    ---

    Learn about a standard workflow for multi-modal preference datasets like image generation preference.

    [:octicons-arrow-right-24: Tutorial](image_preference.ipynb)

</div>
