name: "🆕 Feature request"
description: Cool new ideas for the project
title: "[FEATURE]"
labels: ["enhancement"]
assignees: []
type: "Feature"
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to suggest a new feature!

  - type: textarea
    id: problem
    attributes:
      label: Is your feature request related to a problem?
      description: A clear and concise description of what the problem is.
      placeholder: I'm always frustrated when [...]
    validations:
      required: false

  - type: textarea
    id: solution
    attributes:
      label: Describe the solution you'd like
      description: A clear and concise description of what you want to happen.
      placeholder: I would like to see...
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Describe alternatives you've considered
      description: A clear and concise description of any alternative solutions or features you've considered.
      placeholder: I've also thought about...
    validations:
      required: false

  - type: textarea
    id: context
    attributes:
      label: Additional context
      description: Add any other context or screenshots about the feature request here.
      placeholder: Here are some more details and screenshots...
    validations:
      required: false

  - type: dropdown
    id: impact
    attributes:
      label: Impact
      description: How would you rate the impact of this feature?
      options:
        - Low (nice to have)
        - Medium (would improve experience)
        - High (would significantly enhance functionality)
    validations:
      required: false

  - type: dropdown
    id: user_type
    attributes:
      label: User Perspective
      description: From which perspective are you requesting this feature?
      options:
        - Researcher/Scientist
        - Research Manager
        - Data Scientist
        - Software Developer
        - Designer
        - Other
      multiple: true
    validations:
      required: false
