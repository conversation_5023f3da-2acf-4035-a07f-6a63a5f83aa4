# Project information
site_name: Extralit Docs
site_url: https://docs.extralit.ai/
site_author: Extralit
site_description: Data-centric tool for AI builders
copyright: Copyright &copy; 2023 - 2025 Extralit

# Repository
repo_name: extralit/extralit
repo_url: https://github.com/extralit/extralit/
edit_uri: edit/main/extralit/docs/

extra:
  version:
    provider: mike
  social:
    - icon: fontawesome/brands/slack
      link: https://join.slack.com/t/extralit/shared_invite/zt-32blg3602-0m0XewPBXF7776BQ3m7ZlA
  analytics:
    provider: google
    property: G-SQHP22W3F6
    feedback:
      title: Was this page helpful?
      ratings:
        - icon: material/thumb-up-outline
          name: This page was helpful
          data: 1
          note: >-
            Thanks for your feedback!
        - icon: material/thumb-down-outline
          name: This page could be improved
          data: 0
          note: >-
            Thanks for your feedback! Help us improve this page by
            <a href="https://github.com/extralit/extralit/issues/new/?title=[Feedback]+{title}+-+{url}" target="_blank" rel="noopener">opening a GitHub issue</a>.
extra_css:
  - stylesheets/extra.css

theme:
  name: material
  logo: assets/logo.png
  favicon: assets/favicon.ico
  features:
    - navigation.instant
    - navigation.sections
    - navigation.tabs
    - navigation.footer
    - navigation.top
    - navigation.tracking
    - navigation.path
    - toc.follow
    - content.code.copy
    - content.code.annotate
    - content.action.edit
    - content.tooltips
    - search.suggest
    - search.highlight
    - search.share
  icon:
    repo: fontawesome/brands/github
  palette:
    - media: "(prefers-color-scheme)"
      primary: white
      toggle:
        icon: material/brightness-auto
        name: Switch to light mode

    # Palette toggle for light mode
    - media: "(prefers-color-scheme: light)"
      scheme: default
      primary: white
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode

    # Palette toggle for dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      primary: custom
      toggle:
        icon: material/brightness-4
        name: Switch to system preference

watch:
  - src/extralit

# Extensions
markdown_extensions:
  - abbr
  - attr_list
  - md_in_html
  - admonition
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.snippets:
      auto_append:
        - docs/glossary.md
  - pymdownx.superfences
  - pymdownx.details
  - pymdownx.tabbed:
      alternate_style: true
  - footnotes
  - tables
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator:
        !!python/name:material.extensions.emoji.to_svg # activating permalink: true makes the anchor link works in the notebooks


  - toc:
      permalink: true

plugins:
  - search
  - open-in-new-tab
  - gen-files:
      scripts:
        - docs/scripts/gen_changelog.py
        # - docs/scripts/gen_popular_issues.py
        # - docs/scripts/gen_ref_pages.py
      enabled: !ENV [CI, false] # enables the plugin only during continuous integration (CI), disabled on local build
  - literate-nav:
      nav_file: SUMMARY.md
  - section-index
  - mkdocstrings:
      handlers:
        python:
          options:
            show_inheritance_diagram: false
            show_source: true # include source code
            # Headings
            heading_level: 3
            show_root_heading: true # show the python path of the class
            show_root_toc_entry: true # show the toc entry for the root class
            show_root_full_path: false # display "diffrax.asdf" not just "asdf"
            show_object_full_path: false # display "diffrax.asdf" not just "asdf"
            show_symbol_type_heading: true
            show_symbol_type_toc: true
            # Members
            inherited_members: true # allow looking up inherited methods
            members_order: source # order methods according to their order of definition in the source code, not alphabetical order
            show_labels: true
            # Docstring
            docstring_style: google # more info: https://sphinxcontrib-napoleon.readthedocs.io/en/latest/example_google.html
            show_if_no_docstring: false
            # Signature
            separate_signature: false
            show_signature_annotations: false
  - social:
      enabled: !ENV [CI, false] # enables the plugin only during continuous integration (CI), disabled on local build
  - mknotebooks
  - material-plausible

# Documentation Structure
nav:
  - Overview: index.md

  - Getting Started:
      - Installation: getting_started/installation.md
      - Quickstart: getting_started/quickstart.md
      - Development Setup: getting_started/development_setup.md
      - Deploy with Docker: getting_started/how-to-deploy-argilla-with-docker.md
      - Deploy with Hugging Face Spaces: getting_started/how-to-configure-argilla-on-huggingface.md

  - User Guide:
      - user_guide/index.md
      - Core concepts: user_guide/core_concepts.md
      - Overview: user_guide/overview.md
      - Data Pipeline:
          - Schema definition: user_guide/schema_definition.md
          # - Importing documents: user_guide/document_import.md
          # - Text and table OCR: user_guide/text_table_ocr.md
          # - LLM-assisted extraction: user_guide/llm_extraction.md
          # - Consensus review: user_guide/consensus_review.md
          # - Data validation: user_guide/data_validation.md
          # - Extraction status: user_guide/extraction_status.md
      - User Interface:
          - Using the command-line interface (CLI): user_guide/command_line_interface.md
      #     - Navigation and layout: user_guide/navigation_layout.md
      #     - Create and manage workspace: user_guide/create_manage_workspace.md
      #     - Search and filter records: user_guide/search_filter.md
      #     - Performing extractions: user_guide/performing_extractions.md
      #     - Reviewing and validating data: user_guide/review_validate_data.md
      - Advanced Usage:
          - Customizing multiple extraction schemas: user_guide/multi_schemas.md
          # - Optimizing LLM performance: user_guide/optimizing_llm_performance.md
          # - Using the API: user_guide/using_the_api.md

  - Administrator Guide:
      - admin_guide/index.md
      - Setup & Deployment:
          - HF Spaces & Docker deployment: admin_guide/docker_deployment.md
          - K8s deployment: admin_guide/k8s_deployment.md
          - Upgrading: admin_guide/upgrading.md
          # - File storage configuration: admin_guide/file_storage.md
      - Server Management:
          - Manage users and credentials: admin_guide/user.md
          - Manage workspaces: admin_guide/workspace.md
          - Create, update and delete datasets: admin_guide/dataset.md
          - Add, update, and delete records: admin_guide/record.md
          - Distribute the annotation task: admin_guide/distribution.md
          - Annotate datasets: admin_guide/annotate.md
          - Query and filter records: admin_guide/query.md
          - Import and export datasets: admin_guide/import_export.md
      - Advanced:
          - Custom fields with layout templates: admin_guide/custom_fields.md
          - Use webhooks to respond to server events:
              - admin_guide/webhooks.md
              - Webhooks internals: admin_guide/webhooks_internals.md
          - Use Markdown to format rich content: admin_guide/use_markdown_to_format_rich_content.md
          - Migrate users, workspaces and datasets to Extralit V2: admin_guide/migrate_from_legacy_datasets.md
          - Custom fields with layout templates: admin_guide/custom_fields.md
  - Tutorials:
      - tutorials/index.md
      - Text classification: tutorials/text_classification.ipynb
      - Token classification: tutorials/token_classification.ipynb
      - Image classification: tutorials/image_classification.ipynb
      - Image preference: tutorials/image_preference.ipynb
  - API Reference:
      - Extralit Python SDK: reference/extralit/
      - FastAPI Server:
          - Server configuration: reference/extralit-server/configuration.md
          - OAuth2 configuration: reference/extralit-server/oauth2_configuration.md
      - Telemetry:
          - Server Telemetry: reference/extralit-server/telemetry.md
  - FAQ: getting_started/faq.md
  - Community:
      - community/index.md
      - How to contribute?: community/contributor.md
      - Developer documentation: community/developer.md
      - Add a new language to Extralit: community/adding_language.md
      - Issue dashboard: community/popular_issues.md
      - Changelog: community/changelog.md
  - UI Demo ↗:
      - https://extralit-public-demo.hf.space/sign-in
