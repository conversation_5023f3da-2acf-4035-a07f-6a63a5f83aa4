---
description: These are the how-to guides for researcher users to perform a literature data extraction job. They provide step-by-step instructions for common scenarios, including detailed explanations and code samples.
hide: toc
---

# User guides

>:warning: This page is currently under construction. Please check back later for updates.

These guides provide step-by-step instructions for common scenarios, including detailed explanations and code samples. Please read the Core Concepts section before proceeding with the user guides.

<div class="grid cards" markdown>

-   __Core concepts__

    ---

    Learn about the core concepts of Extralit, including the data pipeline, schema definition, and advanced usage.

    [:octicons-arrow-right-24: User guide](core_concepts.md)


</div>


## Data Pipeline

<div class="grid cards" markdown>

-   __Schema definition__

    ---

    Learn what they are and how to create and edit [`SchemaStructure`](schema_definition.md) in Extralit.

    [:octicons-arrow-right-24: User guide](schema_definition.md)

-   __Command Line Interface__

    ---

    Learn how to use Extralit's command line tools to manage workspaces, files, documents, and extraction workflows.

    [:octicons-arrow-right-24: User guide](command_line_interface.md)


</div>

## Advanced Usage

<div class="grid cards" markdown>

-   __Customizing multiple extraction schemas__

    ---

    Learn how to define multiple related schemas in Extralit to extract structured data from scientific papers.

    [:octicons-arrow-right-24: User guide](multi_schemas.md)


</div>