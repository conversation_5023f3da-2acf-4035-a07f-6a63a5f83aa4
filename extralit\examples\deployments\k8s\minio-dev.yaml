# Deploys a new Namespace for the MinIO Pod
# Deploys a new MinIO Pod into the metadata.namespace Kubernetes namespace
#
# The `spec.containers[0].args` contains the command run on the pod
# The `/data` directory corresponds to the `spec.containers[0].volumeMounts[0].mountPath`
# That mount path corresponds to a Kubernetes HostPath which binds `/data` to a local drive or volume on the worker node where the pod runs
#
apiVersion: v1
kind: Pod
metadata:
  labels:
    app: minio
  name: minio
spec:
  template:
    metadata:
      labels:
        app: minio
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 1
            preference:
              matchExpressions:
              - key: role
                operator: In
                values:
                - storage
  containers:
  - name: minio
    image: quay.io/minio/minio:RELEASE.2024-08-29T01-40-52Z
    command:
    - /bin/bash
    - -c
    args:
    - minio server /data --console-address :9090
    env:
    - name: MINIO_ACCESS_KEY_FILE
      value: /etc/minio/S3_ACCESS_KEY
    - name: MINIO_SECRET_KEY_FILE
      value: /etc/minio/S3_SECRET_KEY
    volumeMounts:
    - mountPath: /data
      name: storage
    - name: extralit-secrets
      mountPath: /etc/minio
      readOnly: true
  volumes:
  - name: storage
    persistentVolumeClaim:
      claimName: minio-pv-claim
  - name: extralit-secrets
    secret:
      secretName: extralit-secrets
---
apiVersion: v1
kind: Service
metadata:
  name: minio-service
spec:
  type: NodePort
  ports:
    - port: 9000
      targetPort: 9000
      protocol: TCP
  selector:
    app: minio
