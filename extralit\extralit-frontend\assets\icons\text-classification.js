/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'text-classification': {
    width: 15,
    height: 15,
    viewBox: '0 0 15 15',
    data: '<path pid="0" d="M4.439 10.315a.875.875 0 100-1.75.875.875 0 000 1.75zM4.439 13.815a.875.875 0 100-1.75.875.875 0 000 1.75zM4.439 7.69a.875.875 0 100-1.75.875.875 0 000 1.75zM12.314 3.315a.875.875 0 100-1.75.875.875 0 000 1.75zM9.689 4.19a.875.875 0 100-1.75.875.875 0 000 1.75zM12.314 5.94a.875.875 0 100-1.75.875.875 0 000 1.75zM8.814 6.815a.875.875 0 100-1.75.875.875 0 000 1.75zM12.314 11.19a.875.875 0 100-1.75.875.875 0 000 1.75zM11.439 13.815a.875.875 0 100-1.75.875.875 0 000 1.75zM8.814 12.94a.875.875 0 100-1.75.875.875 0 000 1.75zM9.689 10.315a.875.875 0 100-1.75.875.875 0 000 1.75zM7.064 3.315a.875.875 0 100-1.75.875.875 0 000 1.75zM1.814 12.065a.875.875 0 100-1.75.875.875 0 000 1.75zM1.814 8.565a.875.875 0 100-1.75.875.875 0 000 1.75z" _fill="#F97316"/>'
  }
})