/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'kebab': {
    width: 20,
    height: 21,
    viewBox: '0 0 20 21',
    data: '<path pid="0" d="M11.875 4.875a1.875 1.875 0 11-3.75 0 1.875 1.875 0 013.75 0zM11.875 10.5a1.875 1.875 0 11-3.75 0 1.875 1.875 0 013.75 0zM11.875 16.125a1.875 1.875 0 11-3.75 0 1.875 1.875 0 013.75 0z" _fill="#000"/>'
  }
})