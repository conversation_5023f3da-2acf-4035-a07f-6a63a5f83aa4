name: Teardown the PR environment

on:
  workflow_dispatch:
  # pull_request:
  #   types: [closed]

jobs:
  teardown_pr_environment:
    name: Teardown Cloud Run PR environment
    runs-on: ubuntu-latest
    if: github.event.pull_request.head.repo.full_name == github.repository

    # Grant permissions to `GITHUB_TOKEN` for Google Cloud Workload Identity Provider
    permissions:
      contents: read
      id-token: write

    steps:
      - uses: actions/checkout@v4

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v1
        with:
          workload_identity_provider: ${{ secrets.GOOGLE_CLOUD_WIP }}
          service_account: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v1
        with:
          version: ">= 435.0.0"

      - name: Remove PR environment if exists
        run: |
          service_name="extralit-quickstart-pr-${{ github.event.pull_request.number }}"
          services=$(gcloud run services list --project=argilla-ci --format="value(metadata.name)")
          if echo "$services" | grep -q "$service_name"; then
            echo "Service '$service_name' exists. Removing it..."
            gcloud run services delete $service_name --project=argilla-ci --region=europe-southwest1 --quiet
          else
            echo "Service $service_name does not exist."
          fi
