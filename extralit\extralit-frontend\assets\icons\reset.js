/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'reset': {
    width: 16,
    height: 18,
    viewBox: '0 0 16 18',
    data: '<path pid="0" d="M.041 0h2.39v3.055a8.364 8.364 0 11-1.24 11.962l1.936-1.415a5.974 5.974 0 10.987-8.823h3.097v2.39H.04V0z" _fill="#000"/>'
  }
})