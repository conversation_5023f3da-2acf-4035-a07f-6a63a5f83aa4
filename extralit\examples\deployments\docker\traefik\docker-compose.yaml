version: "3.8"

services:
  traefik:
    image: "traefik:v2.10"
    container_name: "traefik"
    command:
      #- "--log.level=DEBUG"
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
    ports:
      - "80:80"
      - "8080:8080"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"

  extralit:
    image: extralitdev/extralit-hf-space:latest
    environment:
      HF_HUB_DISABLE_TELEMETRY: 1
      EXTRALIT_BASE_URL: /extralit
      USERNAME: extralit
      PASSWORD: 12345678
      API_KEY: extralit.apikey
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.extralit.rule=PathPrefix(`/extralit/`)"
      - "traefik.http.routers.extralit.entrypoints=web"
      - "traefik.http.services.extralit.loadbalancer.server.port=6900"
      - "traefik.http.middlewares.extralit-stripprefix.stripprefix.prefixes=/extralit"
      - "traefik.http.middlewares.extralit-stripprefix.stripprefix.forceSlash=false"
      - "traefik.http.routers.extralit.middlewares=extralit-stripprefix"
