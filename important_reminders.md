# Important Reminders for Extralit Project

This document contains crucial architectural decisions, best practices, and key insights from mentor feedback that are essential for understanding and contributing to the Extralit project.

## 📋 Table of Contents
- [Queue Architecture](#queue-architecture)
- [Import Strategy](#import-strategy)
- [Job Management](#job-management)
- [Package Structure](#package-structure)
- [Code Quality](#code-quality)
- [Workflow Architecture](#workflow-architecture)
- [Recent Major Changes](#recent-major-changes)

## 🔄 Queue Architecture

### ✅ **Correct Approach: Priority-Based Queues**
```python
# Use queues to designate PRIORITY, not job types
HIGH_PRIORITY_QUEUE = "high"
NORMAL_PRIORITY_QUEUE = "normal" 
LOW_PRIORITY_QUEUE = "low"

# Submit different job types to same queue based on priority
queue.enqueue(process_pdf_job, pdf_data, queue="high")    # Urgent PDF
queue.enqueue(process_pdf_job, pdf_data, queue="normal")  # Regular PDF
queue.enqueue(process_image_job, image_data, queue="normal")  # Regular image
```

### ❌ **Wrong Approach: Job-Type-Based Queues**
```python
# DON'T create separate queues for different job types
PDF_QUEUE = "pdf_queue"     # ❌ Wrong
IMAGE_QUEUE = "image_queue" # ❌ Wrong
TEXT_QUEUE = "text_queue"   # ❌ Wrong
```

**Why Priority-Based is Better:**
- **Scalability**: More workers can be assigned to high-priority queues
- **Simplicity**: Fewer queues to manage and monitor
- **Resource Management**: Better control over processing order
- **Maintainability**: Easier to add new job types without creating new queues

## 📦 Import Strategy

### ✅ **Correct: Absolute Imports with Full Package Path**
```python
# Always use full path starting with package name
from extralit_server.contexts.ocr.rq_client import cancel_job, enqueue_pdf_extraction
from extralit_server.jobs.pdf_extraction_jobs import extract_pdf_text
from extralit_ocr.extract import extract_markdown_with_hierarchy
```

### ❌ **Wrong: Relative Imports**
```python
# DON'T use relative imports
from .rq_client import cancel_job  # ❌ Hard to maintain, breaks easily
from ..jobs import extract_pdf     # ❌ Confusing and error-prone
```

**Benefits of Absolute Imports:**
- **Clarity**: Always clear where code is coming from
- **Reliability**: Less likely to break when files are moved
- **IDE Support**: Better autocomplete and navigation
- **Debugging**: Easier to trace import issues

**Requirements for Absolute Imports:**
- Must have proper `pyproject.toml` or `setup.py`
- Package must be installed in editable mode: `uv pip install -e .`

## 🏗️ Job Management

### Key Principles

1. **Use RQ Groups for Job Status Queries**
   - Don't create custom GET job status functions
   - RQ Groups already provide this functionality
   - Avoid code duplication

2. **Job Dependencies and Workflows**
   - Use job dependencies to chain related tasks
   - Store job IDs in workflow records for traceability
   - Always handle job failures gracefully

3. **Job Organization**
   ```python
   # Example of proper job workflow
   def start_pdf_workflow(document_id: str):
       # Enqueue extraction job with dependency
       extraction_job = queue.enqueue(
           extract_pdf_text,
           document_id,
           queue="normal",
           job_timeout="10m"
       )
       
       # Store job ID for tracking
       workflow.update(extraction_job_id=extraction_job.id)
       
       return extraction_job
   ```

## 📁 Package Structure

### Current Architecture

#### extralit-server
- **Purpose**: Main API server and job orchestration
- **Key Components**:
  - `src/extralit_server/api/handlers/` - API endpoints
  - `src/extralit_server/jobs/` - Job definitions
  - `src/extralit_server/contexts/` - Business logic contexts

#### extralit-hf-space  
- **Purpose**: OCR processing service (now `extralit_ocr` package)
- **Key Components**:
  - `extralit_ocr/extract.py` - Core PDF extraction logic
  - `extralit_ocr/jobs.py` - RQ job orchestration
  - `extralit_ocr/schemas.py` - Pydantic models for validation

### Package Setup Requirements
```bash
# In extralit-hf-space directory
uv pip install -e .           # Install in editable mode
pre-commit install           # Setup code formatting hooks
```

## 🎯 Code Quality

### Pre-commit Hooks
- **YAML formatting and linting**
- **Python code formatting (Black/Ruff)**
- **Import sorting**
- **Type checking**

### Type Annotations
```python
# Use modern Python type hints
def extract_text(bbox: tuple[int, int, int, int]) -> dict[str, Any]:
    """Modern type annotations for better clarity."""
    pass

# Instead of old-style
def extract_text(bbox):  # ❌ No type info
    pass
```

### Configuration Management
```python
# Use Pydantic for robust configuration
class ExtractionConfig(BaseModel):
    """Type-safe configuration with validation."""
    timeout: int = 300
    max_pages: int = 1000
    output_format: str = "markdown"
```

## 🔄 Workflow Architecture

### PDF Processing Workflow

1. **Job Submission**: PDF uploaded to extralit-server
2. **Queue Routing**: Job sent to appropriate priority queue
3. **OCR Processing**: extralit_ocr package processes the PDF
4. **Result Storage**: Extracted text stored with metadata
5. **Status Updates**: Job status tracked via RQ Groups

### Key Workflow Components

#### extralit-server Side
```python
# Workflow orchestration
def start_pdf_workflow(document_id: str):
    job = enqueue_pdf_extraction(
        document_id=document_id,
        queue="PDF_OCR_QUEUE",  # Specific OCR queue
        depends_on=preprocessing_job
    )
    return job
```

#### extralit-hf-space Side
```python
# Actual OCR processing
@job('PDF_OCR_QUEUE')
def process_pdf_extraction(document_id: str):
    # Download from S3
    # Extract text using PyMuPDF
    # Update document metadata
    # Handle errors robustly
    pass
```

## 🔄 Recent Major Changes

### extralit-server Changes
1. **Queue Improvements**: Added `PDF_OCR_QUEUE` for better job organization
2. **Workflow Updates**: PDF workflow now uses new queue with proper dependencies
3. **Code Cleanup**: Removed duplicate job status functions (use RQ Groups instead)
4. **Import Fixes**: Moved to absolute imports throughout

### extralit-hf-space Changes  
1. **Package Restructure**: Created `extralit_ocr` package with proper namespace
2. **Build System**: Added `pyproject.toml` for proper Python packaging
3. **Worker Addition**: New `worker_pdf_ocr` process in Procfile
4. **Code Migration**: Moved extraction logic from extralit-server to here
5. **Type Safety**: Improved type annotations throughout
6. **CI/CD**: Added pre-commit hooks and improved workflows

## 🛠️ Development Tools

### Essential Tools
```bash
# For monitoring RQ jobs and queues
pip install rq-dashboard

# For development setup
uv pip install -e .          # Editable install
pre-commit install          # Code quality hooks
```

### Debugging
- **RQ Dashboard**: Visual interface for monitoring jobs, queues, and workers
- **Live Logs**: Integration tests now show live logs for better debugging
- **Job Tracing**: Store job IDs in workflow records for easy tracking

## ⚠️ Common Pitfalls to Avoid

1. **Don't create queues for job types** - Use priority-based queues instead
2. **Avoid relative imports** - Always use full package paths
3. **Don't duplicate RQ functionality** - Use built-in RQ Groups for job status
4. **Don't skip type annotations** - Use modern Python type hints
5. **Don't forget editable installs** - Required for absolute imports to work
6. **Don't skip pre-commit setup** - Essential for code quality
7. **Don't over-engineer solutions** - Write minimal code with good structure
8. **Don't add excessive comments** - Code should be self-explanatory
9. **Don't commit test files in early prototypes** - Use for validation only
10. **Don't commit PDFs/data files** - Use .gitignore or direct file paths

## 📚 Key Learnings

1. **Architecture First**: Proper queue and package architecture prevents many issues
2. **Type Safety**: Modern Python type hints catch errors early
3. **Code Quality**: Pre-commit hooks maintain consistent code style
4. **Job Management**: Use existing RQ features instead of reinventing
5. **Import Strategy**: Absolute imports are more maintainable than relative ones
6. **Minimal Code**: Write code minimally with good structure for easier review and changes
7. **Documentation**: Read official docs thoroughly before implementing features
8. **Testing Strategy**: Tests should follow existing patterns with proper mocks and factories
9. **Design Discussion**: Always discuss implementation decisions before coding
10. **Code Evolution**: Easier to change well-structured minimal code when requirements change

## 🎯 **Dataset and Record Management Best Practices**

### ✅ **Dataset Creation Patterns**
From the Extralit documentation, proper dataset creation follows these patterns:

1. **Dataset Structure**: Use proper `ex.Dataset` and `ex.Settings` classes
   ```python
   # Create dataset with proper settings
   settings = ex.Settings(
       fields=[ex.TextField(name="content")],
       metadata=[ex.TermsMetadataProperty(name="metadata")],
       vectors=[ex.VectorField(name="embeddings", dimensions=1536)]
   )
   
   dataset = ex.Dataset(name="dataset_name", workspace="workspace", settings=settings)
   dataset.create()  # Only call when dataset doesn't exist
   ```

2. **Check Dataset Existence**: Always check before creating
   ```python
   existing_dataset = client.datasets(name="dataset_name", workspace="workspace")
   if existing_dataset is None:
       # Create new dataset
       dataset = ex.Dataset(name="dataset_name", workspace="workspace", settings=settings)
       dataset.create()
   ```

3. **Record Structure**: Follow Extralit record patterns
   ```python
   # Proper record structure for embeddings
   record = {
       "fields": {"content": chunk_content},  # For display
       "metadata": {                          # For filtering/sorting
           "reference": document.reference,
           "chunk_index": index,
           "page_number": page
       },
       "vectors": {"embeddings": embedding_vector}
   }
   ```

### ✅ **Content-Aware Chunking Strategy**
Based on mentor feedback for modern RAG approaches:

1. **Section-First Chunking**: Parse by full sections without character limits initially
2. **Optional Character Chunking**: Only apply `chunk_size` limit as secondary step
3. **Hierarchy Preservation**: Maintain document structure and headers
4. **Configurable Limits**: Allow `chunk_size=None` to disable character chunking

### ✅ **Document Metadata Integration**
For extralit-hf-space PyMuPDF integration:
- Import existing metadata classes from the codebase
- Use document margin from database metadata when calling PyMuPDF
- Minimal implementation - just a few lines importing existing code


## 🔧 **Mentor Code Review Guidelines**

### ✅ **Content-Aware Chunking Strategy (Modern RAG)**
Based on mentor feedback for embedding/chunking implementations:

1. **Section-First Approach**: Parse by full sections without character limits initially
   ```python
   def chunk_markdown(markdown_text: str, chunk_size: Optional[int] = None, overlap: int = 200):
       """
       Modern RAG chunking: section-first, then optional character limits.
       
       Args:
           chunk_size: None to disable character chunking, or max chars per chunk
       """
       # First: Parse by sections and preserve hierarchy
       sections = parse_sections_with_hierarchy(markdown_text)
       
       # Second: Apply character chunking only if chunk_size is provided
       if chunk_size is not None:
           sections = apply_character_chunking(sections, chunk_size, overlap)
       
       return sections
   ```

2. **Hierarchy Preservation**: Maintain document structure and headers throughout
3. **Configurable Limits**: Support `chunk_size=None` to disable character chunking entirely

### ✅ **Proper Dataset Creation Patterns**
Follow Extralit documentation patterns exactly:

```python
# ✅ Correct approach
dataset = client.datasets(name=dataset_name, workspace=workspace)
if dataset is None:
    # Create proper settings first
    settings = ex.Settings(
        fields=[ex.TextField(name="content")],
        metadata=[ex.TermsMetadataProperty(name="reference")],
        vectors=[ex.VectorField(name="embeddings", dimensions=1536)]
    )
    # Create dataset with settings
    dataset = ex.Dataset(name=dataset_name, workspace=workspace, settings=settings)
    dataset.create()  # Only call when dataset doesn't exist

# ❌ Wrong approach - don't repeat same operation in except block
try:
    dataset = client.datasets(name=dataset_name, workspace=workspace)
except Exception:
    dataset = client.datasets(name=dataset_name, workspace=workspace)  # This won't work!
```

### ✅ **Code Quality Standards**
Based on mentor feedback to avoid AI-generated appearance:

1. **Minimal Code Structure**: Write minimal code with good structure for easier review/changes
2. **No Excessive Comments**: Code should be self-explanatory
3. **No Excessive Print Statements**: Use proper logging or minimal output
4. **Proper Error Handling**: Don't just repeat failed operations
5. **Follow Existing Patterns**: Use established patterns from codebase

### ✅ **Record Structure Consistency**
Maintain proper separation as per mentor requirements:

```python
record = {
    "fields": {                    # For displaying content in UI
        "content": chunk_content,
        "header": chunk_header,
    },
    "metadata": {                  # For filtering and sorting
        "reference": document.reference,
        "chunk_index": index,
        "page_number": page,
        "header_hierarchy": hierarchy_string,
    },
    "vectors": {                   # For similarity search
        "embeddings": embedding_vector
    }
}
```

### ✅ **Implementation Discussion First**
Key learnings from mentor feedback:

1. **Discuss Before Coding**: Always discuss implementation decisions before coding
2. **Read Documentation**: Thoroughly read official docs before implementing features
3. **Test Code Paths**: Actually run and test the code, don't submit untested AI-generated code
4. **Ask Questions**: When uncertain about chunking strategies or other approaches, ask first
5. **Iterative Development**: Start minimal, then expand based on requirements

### ❌ **Common Mistakes to Avoid**
1. **Don't commit test files in early prototypes** - use for validation only
2. **Don't commit PDFs/data files** - use .gitignore or direct file paths
3. **Don't repeat failed operations** - properly handle errors
4. **Don't use excessive print statements** - keep code clean
5. **Don't implement outdated patterns** - use modern RAG approaches
6. **Don't skip testing code paths** - ensure code actually works before submission

---

*This document should be updated as the project evolves and new architectural decisions are made.*