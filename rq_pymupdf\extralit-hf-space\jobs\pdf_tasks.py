import os
import tempfile
from typing import Optional, List, Union

import fitz  # PyMuPDF
import requests
import boto3


def _download_to_tmp(url: str) -> str:
    fd, path = tempfile.mkstemp(suffix=".pdf")
    os.close(fd)
    with requests.get(url, stream=True, timeout=30) as r:
        r.raise_for_status()
        with open(path, "wb") as f:
            for chunk in r.iter_content(1 << 20):
                if chunk:
                    f.write(chunk)
    return path


def _upload_text(bucket: str, key: str, content: str) -> str:
    s3 = boto3.client("s3")
    s3.put_object(Bucket=bucket, Key=key, Body=content.encode("utf-8"), ContentType="text/plain")
    return f"s3://{bucket}/{key}"


def extract_text_from_pdf(
    input_uri: str,
    pages: Optional[Union[List[int], str]] = None,
    output_bucket: Optional[str] = None,
    output_key: Optional[str] = None,
) -> dict:
    """
    Extract text from a PDF at input_uri (https or s3). Returns either the text or a pointer to stored text.

    - input_uri: https(s):// signed URL, or s3://bucket/key
    - pages: list of 0-based page indexes, or string ranges like "0,2,5-7"
    - output_bucket/output_key: if provided, upload the text and return a URI pointer instead of raw text
    """
    # Resolve input
    local_path = None
    try:
        if input_uri.startswith("s3://"):
            # Simple s3 download
            _, rest = input_uri.split("s3://", 1)
            bucket, key = rest.split("/", 1)
            s3 = boto3.client("s3")
            fd, local_path = tempfile.mkstemp(suffix=".pdf")
            os.close(fd)
            s3.download_file(bucket, key, local_path)
        else:
            local_path = _download_to_tmp(input_uri)

        # Parse pages arg
        page_indexes: Optional[List[int]] = None
        if isinstance(pages, str):
            idxs = []
            for part in pages.split(","):
                part = part.strip()
                if "-" in part:
                    a, b = part.split("-", 1)
                    idxs.extend(range(int(a), int(b) + 1))
                elif part:
                    idxs.append(int(part))
            page_indexes = idxs
        elif isinstance(pages, list):
            page_indexes = pages

        # Extract text
        text_chunks = []
        with fitz.open(local_path) as doc:
            if page_indexes is None:
                page_indexes = list(range(len(doc)))
            for i in page_indexes:
                page = doc.load_page(i)
                text_chunks.append(page.get_text())

        full_text = "\n".join(text_chunks)

        # Return or store
        if output_bucket and output_key:
            uri = _upload_text(output_bucket, output_key, full_text)
            return {"ok": True, "result_uri": uri, "pages": page_indexes}
        else:
            # Be careful with large PDFs; prefer returning a URI in production
            return {"ok": True, "text": full_text, "pages": page_indexes}
    finally:
        if local_path and os.path.exists(local_path):
            try:
                os.remove(local_path)
            except OSError:
                pass