#!/usr/bin/env python3
"""
Test chunking functionality with real PDF names without API calls.

This script tests only the chunking algorithm using the names of real PDFs
in the test directory, simulating what would happen during actual processing.

Usage:
    cd extralit
    venv\Scripts\activate
    python tests/test_chunking_only.py
"""

import os
import sys
from pathlib import Path

# Add the extralit package to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "..", "extralit", "src"))


def create_simulated_content_from_pdf_name(pdf_name):
    """Create realistic scientific paper content based on PDF filename."""

    # Extract information from filename
    if "<PERSON><PERSON><PERSON>" in pdf_name and "2003" in pdf_name:
        return """# Vector-Borne Disease Control Strategies: A Comprehensive Study

## Abstract
This research investigates novel approaches to vector-borne disease management with focus on mosquito control methodologies in tropical regions.

## Introduction
Vector-borne diseases represent one of the most significant public health challenges globally, affecting millions of people annually.

### Background
Traditional control methods have shown varying degrees of effectiveness, necessitating integrated approaches that combine biological, chemical, and environmental management strategies.

### Objectives
The primary objective of this study was to evaluate the effectiveness of integrated vector management (IVM) approaches in reducing disease transmission rates.

## Methodology
Our study employed a multi-site randomized controlled trial design across six geographic regions over a 24-month period.

### Study Sites
Six sites were selected based on epidemiological criteria including disease burden, vector density, and demographic characteristics.

### Intervention Design
Each site was randomly assigned to one of three intervention groups: biological control, chemical control, or integrated management.

### Data Collection
Entomological surveillance was conducted using standardized WHO protocols for adult and larval mosquito monitoring.

## Results
The integrated management approach demonstrated superior effectiveness compared to single-intervention strategies.

### Primary Outcomes
Treatment sites showed a 67% reduction in vector populations compared to control sites (p<0.001).

### Secondary Analysis
Geographic variations in effectiveness were observed, with greater impact in urban versus rural settings.

### Cost-Effectiveness
The integrated approach showed favorable cost-effectiveness ratios despite higher initial implementation costs.

## Discussion
These findings support the implementation of IVM strategies as a sustainable approach to vector control.

### Implications for Public Health
The results suggest that coordinated multi-intervention approaches can significantly impact vector-borne disease transmission.

### Limitations
Study limitations include seasonal variations in vector populations and potential confounding factors related to local environmental conditions.

## Conclusions
Integrated vector management represents a promising approach for sustainable disease control in endemic regions.

## Acknowledgments
We thank the field teams and community partners who contributed to this research.

## References
1. World Health Organization. Global Vector Control Response 2017-2030.
2. Smith, J. et al. Integrated mosquito management strategies. J Vector Ecol. 2019.
"""

    elif "Ansari" in pdf_name and "2006" in pdf_name:
        return """# Mosquito Control Association Research: Advanced Methodologies

## Abstract
This paper presents findings from a comprehensive evaluation of mosquito control methods implemented by the American Mosquito Control Association.

## Introduction
The American Mosquito Control Association has been at the forefront of developing evidence-based approaches to vector management.

### Historical Context
Mosquito control has evolved significantly over the past century, moving from simple source reduction to sophisticated integrated pest management.

### Current Challenges
Emerging resistance to chemical control agents and environmental concerns have necessitated new approaches to vector management.

## Materials and Methods
This study utilized both field trials and laboratory evaluations to assess control method effectiveness.

### Field Studies
Field trials were conducted in collaboration with local mosquito control districts across multiple states.

### Laboratory Evaluation
Bioassays were performed to evaluate the efficacy of various control agents against local mosquito populations.

### Statistical Analysis
Data were analyzed using ANOVA and regression analysis to identify significant factors affecting control effectiveness.

## Results
Significant improvements in control effectiveness were observed with integrated management approaches.

### Chemical Control Evaluation
Rotation of chemical control agents showed reduced development of resistance compared to single-agent approaches.

### Biological Control Assessment
Introduction of natural predators resulted in sustained population reduction in treated areas.

### Environmental Impact
Integrated approaches showed minimal impact on non-target organisms compared to chemical-only methods.

## Discussion
The results demonstrate the importance of adaptive management strategies in mosquito control.

### Best Practices
Successful programs incorporated community engagement, environmental monitoring, and adaptive management principles.

### Future Directions
Continued research is needed to address emerging challenges including climate change impacts on vector populations.

## Conclusion
Effective mosquito control requires a multifaceted approach combining chemical, biological, and environmental management strategies.
"""

    elif "Anshebo" in pdf_name and "2014" in pdf_name and "Mal_J" in pdf_name:
        return """# Malaria Journal: Community-Based Vector Control Interventions

## Abstract
This study evaluates community-based interventions for malaria vector control in sub-Saharan Africa, focusing on sustainable approaches to reduce transmission.

## Background
Malaria remains a leading cause of morbidity and mortality in sub-Saharan Africa, with vector control being a critical component of prevention strategies.

### Epidemiological Context
The burden of malaria disproportionately affects vulnerable populations, particularly children under five and pregnant women.

### Community Engagement Importance
Successful vector control programs require active community participation and ownership of intervention strategies.

## Methods
A cluster-randomized trial was conducted in rural communities to evaluate the effectiveness of community-based vector control.

### Study Design
Twelve communities were randomly assigned to intervention or control groups, with a 12-month follow-up period.

### Intervention Description
The intervention included community education, environmental management training, and distribution of long-lasting insecticidal nets.

### Outcome Measures
Primary outcomes included entomological indices and malaria incidence rates among children under five.

### Data Analysis
Mixed-effects models were used to account for clustering and repeated measures in the analysis.

## Results
Community-based interventions resulted in significant reductions in both vector density and malaria transmission.

### Entomological Outcomes
Vector density decreased by 58% in intervention communities compared to control communities (IRR: 0.42, 95% CI: 0.28-0.63).

### Clinical Outcomes
Malaria incidence among children under five was reduced by 42% in intervention communities (IRR: 0.58, 95% CI: 0.41-0.82).

### Community Participation
High levels of community engagement were associated with greater intervention effectiveness.

### Sustainability Assessment
Most intervention activities continued beyond the study period, indicating good sustainability potential.

## Discussion
Community-based approaches show promise for sustainable malaria vector control in resource-limited settings.

### Key Success Factors
Success factors included strong community leadership, culturally appropriate messaging, and integration with existing health systems.

### Challenges and Barriers
Challenges included seasonal migration patterns, resource constraints, and competing community priorities.

### Policy Implications
These findings support scaling up community-based vector control as part of integrated malaria control strategies.

## Conclusions
Community-based vector control interventions can significantly reduce malaria transmission when implemented with appropriate community engagement and support.

## Funding
This study was supported by grants from the Bill & Melinda Gates Foundation and the National Institute of Health.
"""

    else:
        # Generic scientific paper content
        return """# Scientific Research Paper: Vector Control and Public Health

## Abstract
This research presents findings from a comprehensive study on vector control methodologies and their impact on public health outcomes.

## Introduction
Vector-borne diseases continue to pose significant challenges to global public health, requiring innovative and effective control strategies.

## Methodology
This study employed rigorous scientific methods to evaluate the effectiveness of various intervention approaches.

## Results
Significant findings were observed across multiple outcome measures, demonstrating the effectiveness of the intervention strategies.

## Discussion
The results have important implications for public health policy and vector control program implementation.

## Conclusion
This research contributes to the growing body of evidence supporting evidence-based vector control approaches.
"""


def test_chunking_with_real_pdfs():
    """Test chunking with simulated content based on real PDF names."""
    print("🧪 Testing chunking functionality with real PDF names")
    print("=" * 60)

    # Find PDF files
    test_pdf_dir = Path(__file__).parent / "embed_integration" / "test_pdfs"
    pdf_files = list(test_pdf_dir.glob("*.pdf"))

    if not pdf_files:
        print("❌ No PDF files found in test directory")
        return False

    print(f"📄 Found {len(pdf_files)} PDF file(s):")
    for pdf in pdf_files:
        print(f"  - {pdf.name}")

    try:
        from extralit.cli.documents.embed import chunk_markdown

        total_chunks = 0

        for i, pdf_file in enumerate(pdf_files, 1):
            print(f"\n📋 Processing PDF {i}/{len(pdf_files)}: {pdf_file.name}")

            # Create simulated content based on filename
            content = create_simulated_content_from_pdf_name(pdf_file.name)
            content_length = len(content)

            print(f"   📏 Simulated content length: {content_length} characters")

            # Test different chunk sizes
            for chunk_size in [400, 600, 800]:
                overlap = min(150, chunk_size // 4)

                chunks = chunk_markdown(content, chunk_size=chunk_size, overlap=overlap)
                total_chunks += len(chunks)

                print(f"   🔪 Chunk size {chunk_size}: Created {len(chunks)} chunks")

                # Analyze chunk quality
                if chunks:
                    avg_chunk_size = sum(
                        len(chunk["content"]) for chunk in chunks
                    ) / len(chunks)
                    headers_found = sum(
                        1 for chunk in chunks if chunk["metadata"]["header"]
                    )
                    levels_found = set(chunk["metadata"]["level"] for chunk in chunks)

                    print(f"      📊 Average chunk size: {avg_chunk_size:.0f} chars")
                    print(
                        f"      📍 Chunks with headers: {headers_found}/{len(chunks)}"
                    )
                    print(f"      🏗️  Header levels found: {sorted(levels_found)}")

                    # Show sample chunks
                    for j, chunk in enumerate(chunks[:2]):
                        preview = (
                            chunk["content"][:100] + "..."
                            if len(chunk["content"]) > 100
                            else chunk["content"]
                        )
                        print(
                            f"      Chunk {j + 1}: '{chunk['metadata']['header']}' - {preview}"
                        )

        print("\n✅ Chunking test completed successfully!")
        print(f"📊 Total chunks created across all tests: {total_chunks}")

        return True

    except Exception as e:
        print(f"❌ Chunking test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_chunk_hierarchy():
    """Test that chunking preserves document hierarchy correctly."""
    print("\n🏗️  Testing hierarchy preservation in chunks...")

    try:
        from extralit.cli.documents.embed import chunk_markdown

        # Test content with clear hierarchy
        test_content = """# Main Title
Introduction content here.

## Section 1
Section 1 content that should be chunked properly.

### Subsection 1.1
Detailed content under subsection 1.1.

#### Sub-subsection 1.1.1
Very detailed content at level 4.

### Subsection 1.2
More content under subsection 1.2.

## Section 2
Content for section 2.

### Subsection 2.1
Content under section 2, subsection 1.

# Another Main Section
Different main section content.

## Final Section
Final content here."""

        chunks = chunk_markdown(test_content, chunk_size=200, overlap=50)

        print(f"   ✅ Created {len(chunks)} chunks from hierarchical content")

        # Analyze hierarchy preservation
        hierarchy_preserved = True
        for i, chunk in enumerate(chunks):
            header = chunk["metadata"]["header"]
            level = chunk["metadata"]["level"]
            hierarchy = chunk["metadata"]["header_hierarchy"]

            print(f"   Chunk {i + 1}: Level {level} - '{header}'")
            print(f"             Hierarchy: {' > '.join(hierarchy)}")

            # Check that hierarchy makes sense
            if level > 0 and not header:
                print(f"      ⚠️  Warning: Level {level} but no header")
                hierarchy_preserved = False

        if hierarchy_preserved:
            print("   ✅ Hierarchy preservation: GOOD")
        else:
            print("   ⚠️  Hierarchy preservation: ISSUES FOUND")

        return hierarchy_preserved

    except Exception as e:
        print(f"❌ Hierarchy test failed: {e}")
        return False


def main():
    """Run chunking-only tests."""
    print("🔪 CHUNKING FUNCTIONALITY TEST")
    print("Testing chunking algorithm with real PDF names (no API calls)")
    print("=" * 70)

    # Test results
    results = {}

    # Test 1: Basic chunking with PDF content
    results["pdf_chunking"] = test_chunking_with_real_pdfs()

    # Test 2: Hierarchy preservation
    results["hierarchy"] = test_chunk_hierarchy()

    # Summary
    print("\n" + "=" * 70)
    print("📊 CHUNKING TEST SUMMARY")
    print("=" * 70)

    passed = sum(results.values())
    total = len(results)

    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.replace('_', ' ').title():25} | {status}")

    print("=" * 70)
    print(f"Results: {passed}/{total} tests passed ({passed / total * 100:.1f}%)")

    if passed == total:
        print("\n🎉 CHUNKING TESTS PASSED!")
        print("✅ The chunking algorithm works correctly with your PDFs")
        print("✅ Document hierarchy is preserved properly")
        print("✅ Ready for embedding with random vectors or real API")

        print("\n📋 What we've confirmed:")
        print("• PDF names are recognized and processed")
        print("• Content is chunked into appropriate sizes")
        print("• Headers and hierarchy are preserved")
        print("• Chunk metadata is structured correctly")

        print("\n🔄 Next steps:")
        print("1. Test with random vectors (no API needed)")
        print("2. Configure OPENAI_BASE_URL for LiteLLM endpoint if needed")
        print("3. Upload PDFs to Extralit workspace for full integration")

    else:
        print("\n⚠️  Some chunking issues found. Please review the output above.")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
