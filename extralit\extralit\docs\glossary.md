<!-- General -->
*[OSS]: Open Source Software, software with source code that anyone can inspect, modify, and enhance.
*[LLM]: Large Language Model, an AI system trained on vast amounts of text data.
*[LLMs]: Large Language Model, an AI system trained on vast amounts of text data.
*[RAG]: Retrieval-Augmented Generation, a technique that combines information retrieval with text generation.
*[NER]: Named Entity Recognition, a subtask of information extraction that locates and classifies named entities in text into predefined categories.
*[OCR]: Optical Character Recognition, the conversion of images of typed, handwritten, or printed text into machine-encoded text.
*[NLP]: Natural Language Processing, a field of artificial intelligence that focuses on the interaction between computers and humans using natural language.
*[ML]: Machine Learning.
*[MLOps]: Machine Learning Operations. Practices and processes to manage the lifecycle of ML models.
*[K8s]: Kubernetes, an open-source platform designed to automate deploying, scaling, and operating application containers.
*[API]: Application Programming Interface.
*[SDK]: Software Development Kit.
*[CLI]: Command Line Interface.
*[Token]: A single element of a sequence, such as a word or a symbol.
*[HF]: HuggingFace - A platform for deploying and managing open-source AI applications, models and datasets in the cloud.
*[CI/CD]: Continuous Integration and Continuous Deployment, practices that automate the integration of code changes and deployment to production environments.
<!-- Extralit specific -->
*[record]: In Extralit, a record is a structured data entry that contains information about a specific instance, such as a text document, an image, or any other type of data that can be annotated or analyzed. Records are the fundamental units of data in Extralit and can be associated with various metadata, labels, and annotations.
*[records]: In Extralit, a record is a structured data entry that contains information about a specific instance, such as a text document, an image, or any other type of data that can be annotated or analyzed. Records are the fundamental units of data in Extralit and can be associated with various metadata, labels, and annotations.
