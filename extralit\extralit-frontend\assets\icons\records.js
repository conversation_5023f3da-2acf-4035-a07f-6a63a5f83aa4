/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'records': {
    width: 10,
    height: 9,
    viewBox: '0 0 10 9',
    data: '<path pid="0" fill-rule="evenodd" clip-rule="evenodd" d="M9 .715H1.32a.3.3 0 00-.3.3v1.421H9.3v-1.42a.3.3 0 00-.3-.3zm-7.98 4.18v-1.76H9.3v1.76H1.02zm0 .7v1.42a.3.3 0 00.3.3H9a.3.3 0 00.3-.3v-1.42H1.02zm.3-5.58a1 1 0 00-1 1v6a1 1 0 001 1H9a1 1 0 001-1v-6a1 1 0 00-1-1H1.32z" _fill="#000"/>'
  }
})