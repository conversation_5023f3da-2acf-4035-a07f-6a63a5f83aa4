name: Build Extralit server package

concurrency:
  group: ${{ github.workflow }}-${{ github.sha }}
  cancel-in-progress: true

on:
  workflow_dispatch:

  push:
    branches:
      - main
      - develop
      - releases/**
    paths:
      - "extralit-server/**"
      - ".github/workflows/extralit-server.*"

  pull_request:
    types: [opened, edited, synchronize, reopened, ready_for_review]
    paths:
      - "extralit-server/**"
      - ".github/workflows/extralit-server.*"

permissions:
  id-token: write

jobs:
  build:
    name: Build `extralit-server` package
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest

    defaults:
      run:
        shell: bash -l {0}
        working-directory: extralit-server

    services:
      elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch:8.17.0
        ports:
          - 9200:9200
        env:
          discovery.type: single-node
          xpack.security.enabled: false

      postgres:
        image: postgres:14
        env:
          POSTGRES_HOST: localhost
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: extralit
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

      minio:
        image: lazybit/minio
        volumes:
          - /data:/data
        env:
          MINIO_ACCESS_KEY: minioadmin
          MINIO_SECRET_KEY: minioadmin
        options: --name=minio --health-cmd "curl http://localhost:9000/minio/health/live"
        ports:
          - 9000:9000

    env:
      HF_HUB_DISABLE_TELEMETRY: 1
      S3_ENDPOINT: http://localhost:9000
      S3_ACCESS_KEY: minioadmin
      S3_SECRET_KEY: minioadmin

    steps:
      - name: Checkout Code 🛎
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          pyproject-file: "extralit-server/pyproject.toml"
          enable-cache: true
          cache-local-path: ~/.cache/uv
          cache-dependency-glob: "extralit-server/pdm.lock"

      - name: Install PDM
        run: uv tool install pdm

      - name: Install dependencies
        run: |
          pdm config use_uv true
          pdm config python.install_root "$(uv python dir)"
          pdm install --dev

      - name: Run tests 📈
        id: run-tests
        continue-on-error: true
        env:
          HF_TOKEN_EXTRALIT_INTERNAL_TESTING: ${{ secrets.HF_TOKEN_EXTRALIT_INTERNAL_TESTING }}
        run: |
          EXTRALIT_DATABASE_URL=postgresql://postgres:postgres@localhost:5432/extralit
          EXTRALIT_ELASTICSEARCH=http://localhost:9200
          EXTRALIT_SEARCH_ENGINE=elasticsearch
          pdm test-cov tests/unit

      - name: Upload test coverage
        if: always()
        uses: codecov/codecov-action@v5.4.3
        with:
          files: coverage.xml
          flags: extralit-server
          token: ${{ secrets.CODECOV_TOKEN }}

      - name: Check test status
        if: steps.run-tests.outcome == 'failure'
        run: exit 1

      - name: Upload test results to Codecov
        if: ${{ !cancelled() }}
        uses: codecov/test-results-action@v1
        with:
          flags: extralit-server
          token: ${{ secrets.CODECOV_TOKEN }}

      # This section is used to build the frontend and copy the build files to the server.
      # In the future, static files should be downloaded after the frontend is built and uploaded as an artifact.
      - name: Setup Node.js for frontend dependencies
        uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Install frontend dependencies
        working-directory: extralit-frontend
        env:
          BASE_URL: "@@baseUrl@@"
          DIST_FOLDER: ./dist
        run: |
          npm install
          npm run build
      # End of frontend build section
      - name: Build package
        run: |
          cp -r ../extralit-frontend/dist src/extralit_server/static
          pdm build

      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: extralit-server
          path: extralit-server/dist

  build_docker_images:
    name: Build docker images
    uses: ./.github/workflows/extralit-server.build-docker-images.yml
    if: |
      github.ref == 'refs/heads/main'
      || github.ref == 'refs/heads/develop'
      || contains(github.ref, 'releases/')
      || github.event_name == 'workflow_dispatch'
      || (github.event_name == 'pull_request' && !github.event.pull_request.head.repo.fork && !github.event.pull_request.draft)
    needs:
      - build
    with:
      is_release: ${{ github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch' }}
      publish_latest: ${{ github.ref == 'refs/heads/main' }}
    secrets: inherit

  # This job will publish extralit-server python package into PyPI repository
  publish_release:
    name: Publish Release
    runs-on: ubuntu-latest
    if: ${{ github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch' }}

    needs:
      - build
      - build_docker_images

    defaults:
      run:
        shell: bash -l {0}
        working-directory: extralit-server

    permissions:
      # This permission is needed for private repositories.
      # contents: read
      # IMPORTANT: this permission is mandatory for trusted publishing on PyPI
      id-token: write

    steps:
      - name: Checkout Code 🛎
        uses: actions/checkout@v4

      - name: Update repo visualizer
        uses: githubocto/repo-visualizer@0.7.1
        with:
          root_path: "extralit-server/"
          excluded_paths: "dist,build,node_modules,docs,tests,.swm,assets,.github,package-lock.json,pdm.lock"
          excluded_globs: "*.spec.js;**/*.{png,jpg,svg,md};**/!(*.module).ts,**/__pycache__/,**/__mocks__/,LICENSE*,**/.gitignore,**/*.egg-info/,**/.*/"
          output_file: "repo-visualizer.svg"
          should_push: false

      - name: Upload repo visualizer diagram as artifact
        uses: actions/upload-artifact@v4
        with:
          name: repo-visualizer
          path: repo-visualizer.svg
          retention-days: 10

      - name: Download python package
        uses: actions/download-artifact@v4
        with:
          name: extralit-server
          path: extralit-server/dist

      - name: Setup PDM
        uses: pdm-project/setup-pdm@v4
        with:
          python-version-file: extralit-server/pyproject.toml
          cache-dependency-path: extralit-server/pdm.lock
          cache: true

      - name: Read package info
        run: |
          PACKAGE_VERSION=$(pdm show --version)
          PACKAGE_NAME=$(pdm show --name)
          echo "PACKAGE_VERSION=$PACKAGE_VERSION" >> $GITHUB_ENV
          echo "PACKAGE_NAME=$PACKAGE_NAME" >> $GITHUB_ENV
          echo "$PACKAGE_NAME==$PACKAGE_VERSION"

      - name: Publish Package to PyPI test environment 🥪
        continue-on-error: true
        run: |
          pdm publish --no-build --repository testpypi --username __token__ --password ${{ secrets.AR_TEST_PYPI_API_TOKEN }}

      - name: Test Installing 🍿
        continue-on-error: true
        run: |
          pip install --index-url https://test.pypi.org/simple --no-deps  $PACKAGE_NAME==$PACKAGE_VERSION

      - name: Publish Package to PyPI 🥩
        if: github.ref == 'refs/heads/main'
        run: |
          pdm publish --no-build --username __token__ --password ${{ secrets.AR_PYPI_API_TOKEN }}
