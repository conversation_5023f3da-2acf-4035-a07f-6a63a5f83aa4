# Copyright 2024-present, Extralit Labs, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import json
import os
import warnings
from collections.abc import Iterator, Sequence
from functools import cached_property
from pathlib import Path
from typing import TYPE_CHECKING, Literal, Optional, Union
from uuid import UUID

from extralit._exceptions import ExtralitAPIError, ExtralitSerializeError, SettingsError
from extralit._models._dataset import DatasetModel
from extralit._resource import Resource
from extralit.settings._field import Field, FieldBase, _field_from_dict, _field_from_model
from extralit.settings._io import build_settings_from_repo_id
from extralit.settings._metadata import <PERSON>ada<PERSON><PERSON><PERSON>, MetadataPropertyBase, MetadataType
from extralit.settings._question import QuestionBase, QuestionType, _question_from_dict, question_from_model
from extralit.settings._task_distribution import TaskDistribution
from extralit.settings._templates import DefaultSettingsMixin
from extralit.settings._vector import VectorField

if TYPE_CHECKING:
    from extralit.datasets import Dataset

__all__ = ["Settings"]


class Settings(DefaultSettingsMixin, Resource):
    """
    Settings class for Extralit Datasets.

    This class is used to define the representation of a Dataset within the UI.
    """

    def __init__(
        self,
        fields: Optional[list[Field]] = None,
        questions: Optional[list[QuestionType]] = None,
        vectors: Optional[list[VectorField]] = None,
        metadata: Optional[list[MetadataType]] = None,
        guidelines: Optional[str] = None,
        allow_extra_metadata: bool = False,
        distribution: Optional[TaskDistribution] = None,
        mapping: Optional[dict[str, Union[str, Sequence[str]]]] = None,
        _dataset: Optional["Dataset"] = None,
    ) -> None:
        """
        Args:
            fields (List[Field]): A list of Field objects that represent the fields in the Dataset.
            questions (List[Union[LabelQuestion, MultiLabelQuestion, RankingQuestion, TextQuestion, RatingQuestion]]):
                A list of Question objects that represent the questions in the Dataset.
            vectors (List[VectorField]): A list of VectorField objects that represent the vectors in the Dataset.
            metadata (List[MetadataField]): A list of MetadataField objects that represent the metadata in the Dataset.
            guidelines (str): A string containing the guidelines for the Dataset.
            allow_extra_metadata (bool): A boolean that determines whether or not extra metadata is allowed in the
                Dataset. Defaults to False.
            distribution (TaskDistribution): The annotation task distribution configuration.
                Default to DEFAULT_TASK_DISTRIBUTION
            mapping (Dict[str, Union[str, Sequence[str]]]): A dictionary that maps incoming data names to Extralit dataset attributes in DatasetRecords.
        """
        super().__init__(client=_dataset._client if _dataset else None)

        self._dataset = _dataset
        self._distribution = distribution or TaskDistribution.default()
        self._mapping = mapping
        self.__guidelines = self.__process_guidelines(guidelines)
        self.__allow_extra_metadata = allow_extra_metadata

        self.__questions = SettingsProperties(self, questions)
        self.__fields = SettingsProperties(self, fields)
        self.__vectors = SettingsProperties(self, vectors)
        self.__metadata = SettingsProperties(self, metadata)

    #####################
    # Properties        #
    #####################

    @property
    def fields(self) -> "SettingsProperties":
        return self.__fields

    @fields.setter
    def fields(self, fields: list[Field]):
        self.__fields = SettingsProperties(self, fields)

    @property
    def questions(self) -> "SettingsProperties":
        return self.__questions

    @questions.setter
    def questions(self, questions: list[QuestionType]):
        self.__questions = SettingsProperties(self, questions)

    @property
    def vectors(self) -> "SettingsProperties":
        return self.__vectors

    @vectors.setter
    def vectors(self, vectors: list[VectorField]):
        self.__vectors = SettingsProperties(self, vectors)

    @property
    def metadata(self) -> "SettingsProperties":
        return self.__metadata

    @metadata.setter
    def metadata(self, metadata: list[MetadataType]):
        self.__metadata = SettingsProperties(self, metadata)

    @property
    def guidelines(self) -> str:
        return self.__guidelines

    @guidelines.setter
    def guidelines(self, guidelines: str):
        self.__guidelines = self.__process_guidelines(guidelines)

    @property
    def allow_extra_metadata(self) -> bool:
        return self.__allow_extra_metadata

    @allow_extra_metadata.setter
    def allow_extra_metadata(self, value: bool):
        self.__allow_extra_metadata = value

    @property
    def distribution(self) -> TaskDistribution:
        return self._distribution

    @distribution.setter
    def distribution(self, value: TaskDistribution) -> None:
        self._distribution = value

    @property
    def mapping(self) -> dict[str, Union[str, Sequence[str]]]:
        return self._mapping

    @mapping.setter
    def mapping(self, value: dict[str, Union[str, Sequence[str]]]):
        self._mapping = value

    @property
    def dataset(self) -> "Dataset":
        return self._dataset

    @dataset.setter
    def dataset(self, dataset: "Dataset"):
        self._dataset = dataset
        self._client = dataset._client

    @cached_property
    def schema(self) -> dict:
        schema_dict = {}

        for field in self.fields:
            schema_dict[field.name] = field

        for question in self.questions:
            schema_dict[question.name] = question

        for vector in self.vectors:
            schema_dict[vector.name] = vector

        for metadata in self.metadata:
            schema_dict[metadata.name] = metadata

        return schema_dict

    @cached_property
    def schema_by_id(self) -> dict[UUID, Union[Field, QuestionType, MetadataType, VectorField]]:
        return {v.id: v for v in self.schema.values()}

    def validate(self) -> None:
        self._validate_empty_settings()
        self._validate_duplicate_names()

        for field in self.fields:
            field.validate()

    #####################
    #  Public methods   #
    #####################

    def get(self) -> "Settings":
        self.fields = self._fetch_fields()
        self.questions = self._fetch_questions()
        self.vectors = self._fetch_vectors()
        self.metadata = self._fetch_metadata()
        self.__fetch_dataset_related_attributes()

        self._update_last_api_call()
        return self

    def create(self) -> "Settings":
        self.validate()

        self._update_dataset_related_attributes()
        self.__fields._create()
        self.__questions._create()
        self.__vectors._create()
        self.__metadata._create()

        self._update_last_api_call()
        return self

    def update(self) -> "Resource":
        self.validate()

        self._update_dataset_related_attributes()
        self.__fields._update()
        self.__questions._update()
        self.__vectors._update()
        self.__metadata._update()
        self.__questions._update()

        self._update_last_api_call()
        return self

    def serialize(self):
        try:
            return {
                "guidelines": self.guidelines,
                "questions": self.__questions.serialize(),
                "fields": self.__fields.serialize(),
                "vectors": self.vectors.serialize(),
                "metadata": self.metadata.serialize(),
                "allow_extra_metadata": self.allow_extra_metadata,
                "distribution": self.distribution.to_dict(),
                "mapping": self.mapping,
            }
        except Exception as e:
            raise ExtralitSerializeError(f"Failed to serialize the settings. {e.__class__.__name__}") from e

    def to_json(self, path: Union[Path, str]) -> None:
        """Save the settings to a file on disk

        Parameters:
            path (str): The path to save the settings to
        """
        if not isinstance(path, Path):
            path = Path(path)
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, "w") as file:
            json.dump(self.serialize(), file)

    @classmethod
    def from_json(cls, path: Union[Path, str]) -> "Settings":
        """Load the settings from a file on disk"""

        with open(path) as file:
            settings_dict = json.load(file)
            return cls._from_dict(settings_dict)

    @classmethod
    def from_hub(
        cls,
        repo_id: str,
        subset: Optional[str] = None,
        feature_mapping: Optional[dict[str, Literal["question", "field", "metadata"]]] = None,
        **kwargs,
    ) -> "Settings":
        """Load the settings from the Hub

        Parameters:
            repo_id (str): The ID of the repository to load the settings from on the Hub.
            subset (Optional[str]): The subset of the repository to load the settings from.
            feature_mapping (Dict[str, Literal["question", "field", "metadata"]]): A dictionary that maps incoming column names to Extralit attributes.
        """

        settings = build_settings_from_repo_id(repo_id=repo_id, feature_mapping=feature_mapping, subset=subset)
        return settings

    def __eq__(self, other: "Settings") -> bool:
        if not (other and isinstance(other, Settings)):
            return False
        return self.serialize() == other.serialize()  # TODO: Create proper __eq__ methods for fields and questions

    def add(
        self, property: Union[Field, VectorField, MetadataType, QuestionType], override: bool = True
    ) -> Union[Field, VectorField, MetadataType, QuestionType]:
        """
        Add a property to the settings

        Args:
            property: The property to add
            override: If True, override the existing property with the same name. Otherwise, raise an error.  Defaults to True.

        Returns:
            The added property

        """
        # review all settings properties and remove any existing property with the same name
        for attributes in [self.fields, self.questions, self.vectors, self.metadata]:
            for prop in attributes:
                if prop.name == property.name:
                    message = f"Property with name {property.name!r} already exists in settings as {prop.__class__.__name__!r}"
                    if override:
                        warnings.warn(message + ". Overriding the existing property.", stacklevel=2)
                        attributes.remove(prop)
                    else:
                        raise SettingsError(message)

        if isinstance(property, FieldBase):
            self.fields.add(property)
        elif isinstance(property, QuestionBase):
            self.questions.add(property)
        elif isinstance(property, VectorField):
            self.vectors.add(property)
        elif isinstance(property, MetadataPropertyBase):
            self.metadata.add(property)
        else:
            raise ValueError(f"Unsupported property type: {type(property).__name__}")
        return property

    #####################
    #  Repr Methods     #
    #####################

    def __repr__(self) -> str:
        return (
            f"Settings(guidelines={self.guidelines}, allow_extra_metadata={self.allow_extra_metadata}, "
            f"distribution={self.distribution}, "
            f"fields={self.fields}, questions={self.questions}, vectors={self.vectors}, metadata={self.metadata})"
        )

    #####################
    #  Private methods  #
    #####################

    @classmethod
    def _from_dict(cls, settings_dict: dict) -> "Settings":
        fields = settings_dict.get("fields", [])
        vectors = settings_dict.get("vectors", [])
        metadata = settings_dict.get("metadata", [])
        guidelines = settings_dict.get("guidelines")
        distribution = settings_dict.get("distribution")
        allow_extra_metadata = settings_dict.get("allow_extra_metadata")
        mapping = settings_dict.get("mapping")

        questions = [_question_from_dict(question) for question in settings_dict.get("questions", [])]
        fields = [_field_from_dict(field) for field in fields]
        vectors = [VectorField.from_dict(vector) for vector in vectors]
        metadata = [MetadataField.from_dict(metadata) for metadata in metadata]

        if distribution:
            distribution = TaskDistribution.from_dict(distribution)

        if mapping:
            mapping = cls._validate_mapping(mapping)

        return cls(
            questions=questions,
            fields=fields,
            vectors=vectors,
            metadata=metadata,
            guidelines=guidelines,
            allow_extra_metadata=allow_extra_metadata,
            distribution=distribution,
            mapping=mapping,
        )

    def _copy(self) -> "Settings":
        instance = self.__class__._from_dict(self.serialize())
        return instance

    def _fetch_fields(self) -> list[Field]:
        models = self._client.api.fields.list(dataset_id=self._dataset.id)
        return [_field_from_model(model) for model in models]

    def _fetch_questions(self) -> list[QuestionType]:
        models = self._client.api.questions.list(dataset_id=self._dataset.id)
        return [question_from_model(model) for model in models]

    def _fetch_vectors(self) -> list[VectorField]:
        models = self.dataset._client.api.vectors.list(self.dataset.id)
        return [VectorField.from_model(model) for model in models]

    def _fetch_metadata(self) -> list[MetadataType]:
        models = self._client.api.metadata.list(dataset_id=self._dataset.id)
        return [MetadataField.from_model(model) for model in models]

    def __fetch_dataset_related_attributes(self):
        # This flow may be a bit weird, but it's the only way to update the dataset related attributes
        # Everything is point that we should have several settings-related endpoints in the API to handle this.
        # POST /api/v1/datasets/{dataset_id}/settings
        # {
        #   "guidelines": ....,
        #   "allow_extra_metadata": ....,
        # }
        # But this is not implemented yet, so we need to update the dataset model directly
        dataset_model = self._client.api.datasets.get(self._dataset.id)

        self.guidelines = dataset_model.guidelines
        self.allow_extra_metadata = dataset_model.allow_extra_metadata

        if dataset_model.distribution:
            self.distribution = TaskDistribution.from_model(dataset_model.distribution)

    def _update_dataset_related_attributes(self):
        # This flow may be a bit weird, but it's the only way to update the dataset related attributes
        # Everything is point that we should have several settings-related endpoints in the API to handle this.
        # POST /api/v1/datasets/{dataset_id}/settings
        # {
        #   "guidelines": ....,
        #   "allow_extra_metadata": ....,
        # }
        # But this is not implemented yet, so we need to update the dataset model directly
        dataset_model = DatasetModel(
            id=self._dataset.id,
            name=self._dataset.name,
            guidelines=self.guidelines,
            allow_extra_metadata=self.allow_extra_metadata,
            distribution=self.distribution._api_model(),
        )
        self._client.api.datasets.update(dataset_model)

    def _validate_empty_settings(self):
        if not all([self.fields, self.questions]):
            message = "Fields and questions are required"
            raise SettingsError(message=message)

    def _validate_duplicate_names(self) -> None:
        dataset_properties_by_name = {}

        for properties in [self.fields, self.questions, self.vectors, self.metadata]:
            for property in properties:
                if property.name in dataset_properties_by_name:
                    raise SettingsError(
                        f"names of dataset settings must be unique, "
                        f"but the name {property.name!r} is used by {type(property).__name__!r} and {type(dataset_properties_by_name[property.name]).__name__!r} "
                    )
                dataset_properties_by_name[property.name] = property

    @classmethod
    def _validate_mapping(cls, mapping: dict[str, Union[str, Sequence[str]]]) -> dict:
        validate_mapping = {}
        for key, value in mapping.items():
            if isinstance(value, str):
                validate_mapping[key] = value
            elif isinstance(value, list) or isinstance(value, tuple):
                validate_mapping[key] = tuple(value)
            else:
                raise SettingsError(f"Invalid mapping value for key {key!r}: {value}")

        return validate_mapping

    def __process_guidelines(self, guidelines):
        if guidelines is None:
            return guidelines

        if not isinstance(guidelines, str):
            raise SettingsError("Guidelines must be a string or a path to a file")

        if os.path.exists(guidelines):
            with open(guidelines) as file:
                return file.read()

        return guidelines


Property = Union[Field, VectorField, MetadataType, QuestionType]


class SettingsProperties(Sequence[Property]):
    """A collection of properties (fields, questions, vectors and metadata) for a dataset settings object.

    This class is used to store the properties of a dataset settings object
    """

    def __init__(self, settings: "Settings", properties: list[Property]):
        self._properties_by_name = {}
        self._settings = settings
        self._removed_properties = []

        for property in properties or []:
            if self._settings.dataset and hasattr(property, "dataset"):
                property.dataset = self._settings.dataset
            self.add(property)

    def __getitem__(self, key: Union[UUID, str, int]) -> Optional[Property]:
        if isinstance(key, int):
            return list(self._properties_by_name.values())[key]
        elif isinstance(key, UUID):
            for prop in self._properties_by_name.values():
                if prop.id and prop.id == key:
                    return prop
        else:
            return self._properties_by_name.get(key)

    def __iter__(self) -> Iterator[Property]:
        return iter(list(self._properties_by_name.values()))

    def __len__(self):
        return len(self._properties_by_name)

    def __eq__(self, other):
        """Check if two instances are equal. Overloads the == operator."""
        if not isinstance(other, SettingsProperties):
            return False
        return self._properties_by_name == other._properties_by_name

    def add(self, property: Property) -> Property:
        self._validate_new_property(property)
        self._properties_by_name[property.name] = property
        setattr(self, property.name, property)
        return property

    def remove(self, property: Union[str, Property]) -> None:
        if isinstance(property, str):
            property = self._properties_by_name.pop(property)
        else:
            property = self._properties_by_name.pop(property.name)

        if property:
            delattr(self, property.name)
            self._removed_properties.append(property)

    def _create(self):
        for property in self:
            try:
                property.dataset = self._settings.dataset
                property.create()
            except ExtralitAPIError as e:
                raise SettingsError(f"Failed to create property {property.name!r}: {e.message}") from e

    def _update(self):
        for item in self:
            try:
                item.dataset = self._settings.dataset
                item.update() if item.id else item.create()
            except ExtralitAPIError as e:
                raise SettingsError(f"Failed to update {item.name!r}: {e.message}") from e

        self._delete()

    def _delete(self):
        for item in self._removed_properties:
            try:
                item.delete()
            except ExtralitAPIError as e:
                raise SettingsError(f"Failed to delete {item.name!r}: {e.message}") from e

    def serialize(self) -> list[dict]:
        return [property.serialize() for property in self]

    def _validate_new_property(self, property: Property) -> None:
        if property.name in self._properties_by_name:
            raise ValueError(f"Property with name {property.name!r} already exists in the collection")

        if property.name in dir(self):
            raise ValueError(f"Property with name {property.name!r} conflicts with an existing attribute")

    def __repr__(self) -> str:
        """Return a string representation of the object."""

        return f"{list(self)!r}"
