{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Text classification"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- **Goal**: Show a standard workflow for a text classification task, including zero-shot suggestions and model fine-tuning.\n", "- **Dataset**: [IMDB](https://huggingface.co/datasets/stanfordnlp/imdb), a dataset of movie reviews that need to be classified as positive or negative.\n", "- **Libraries**: [datasets](https://github.com/huggingface/datasets), [transformers](https://github.com/huggingface/transformers), [setfit](https://github.com/huggingface/setfit)\n", "- **Components**: [TextField](https://docs.argilla.io/latest/reference/argilla/settings/fields/#src.argilla.settings._field.TextField), [LabelQuestion](https://docs.argilla.io/latest/reference/argilla/settings/questions/#src.argilla.settings._question.LabelQuestion), [Suggestion](https://docs.argilla.io/latest/reference/argilla/records/suggestions/), [Query](https://docs.argilla.io/dev/reference/argilla/search/#rgquery_1), [Filter](https://docs.argilla.io/dev/reference/argilla/search/#rgfilter)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting started"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Deploy the Extralit server"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you already have deployed Extralit, you can skip this step. Otherwise, you can quickly deploy Extralit following [this guide](../getting_started/quickstart.md)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Set up the environment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To complete this tutorial, you need to install the Extralit SDK and a few third-party libraries via `pip`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install extralit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install setfit==1.0.3 transformers==4.40.2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's make the required imports:"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["from datasets import Dataset, load_dataset\n", "from setfit import SetFitModel, Trainer, get_templated_dataset, sample_dataset\n", "\n", "import extralit as ex"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You also need to connect to the Extralit server using the `api_url` and `api_key`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Replace api_url with your url if using Docker\n", "# Replace api_key with your API key under \"My Settings\" in the UI\n", "# Uncomment the last line and set your HF_TOKEN if your space is private\n", "client = ex.Extralit(\n", "    api_url=\"https://[your-owner-name]-[your_space_name].hf.space\",\n", "    api_key=\"[your-api-key]\",\n", "    # headers={\"Authorization\": f\"Bearer {HF_TOKEN}\"}\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vibe check the dataset\n", "\n", "We will have a look at [the dataset](https://huggingface.co/datasets/stanfordnlp/imdb) to understand its structure and the kind of data it contains. We do this by using [the embedded Hugging Face Dataset Viewer](https://huggingface.co/docs/hub/main/en/datasets-viewer-embed).\n", "\n", "<iframe\n", "  src=\"https://huggingface.co/datasets/stanfordnlp/imdb/embed/viewer/default/train\"\n", "  frameborder=\"0\"\n", "  width=\"100%\"\n", "  height=\"560px\"\n", "></iframe>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configure and create the Extralit dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we will need to configure the dataset. In the settings, we can specify the guidelines, fields, and questions. If needed, you can also add metadata and vectors. However, for our use case, we just need a text field and a label question, corresponding to the `text` and `label` columns.\n", "\n", "!!! note\n", "    Check this [how-to guide](../admin_guide/dataset.md) to know more about configuring and creating a dataset."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["labels = [\"positive\", \"negative\"]\n", "\n", "settings = ex.Settings(\n", "    guidelines=\"Classify the reviews as positive or negative.\",\n", "    fields=[\n", "        ex.<PERSON><PERSON><PERSON>(\n", "            name=\"review\",\n", "            title=\"Text from the review\",\n", "            use_markdown=False,\n", "        ),\n", "    ],\n", "    questions=[\n", "        ex.LabelQuestion(\n", "            name=\"sentiment_label\",\n", "            title=\"In which category does this article fit?\",\n", "            labels=labels,\n", "        )\n", "    ],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's create the dataset with the name and the defined settings:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset = ex.Dataset(\n", "    name=\"text_classification_dataset\",\n", "    settings=settings,\n", ")\n", "dataset.create()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Add records"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Even if we have created the dataset, it still lacks the information to be annotated (you can check it in the UI). We will use the `imdb` dataset from the [Hugging Face Hub](https://huggingface.co/datasets/stanfordnlp/imdb). Specifically, we will use 100 samples from the `train` split."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["hf_dataset = load_dataset(\"imdb\", split=\"train[:100]\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will easily add them to the dataset using `log` and the mapping, where we indicate that the column `text` is the data that should be added to the field `review`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset.records.log(records=hf_dataset, mapping={\"text\": \"review\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add initial model suggestions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The next step is to add suggestions to the dataset. This will make things easier and faster for the annotation team. Suggestions will appear as preselected options, so annotators will only need to correct them. In our case, we will generate them using a zero-shot SetFit model. However, you can use a framework or technique of your choice."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will start by defining an example training set with the required labels: `positive` and `negative`. Using `get_templated_dataset` will create sentences from the default template: \"This sentence is {label}.\""]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["zero_ds = get_templated_dataset(\n", "    candidate_labels=labels,\n", "    sample_size=8,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we will prepare a function to train the SetFit model.\n", "\n", "!!! note\n", "    For further customization, you can check the [SetFit documentation](https://huggingface.co/docs/setfit/reference/main)."]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def train_model(model_name, dataset):\n", "    model = SetFitModel.from_pretrained(model_name)\n", "\n", "    trainer = Trainer(\n", "        model=model,\n", "        train_dataset=dataset,\n", "    )\n", "\n", "    trainer.train()\n", "\n", "    return model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's train the model. We will use `TaylorAI/bge-micro-v2`, available in the [Hugging Face Hub](https://huggingface.co/TaylorAI/bge-micro-v2)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = train_model(model_name=\"TaylorAI/bge-micro-v2\", dataset=zero_ds)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can save it locally or push it to the Hub. And then, load it from there."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# Save and load locally\n", "# model.save_pretrained(\"text_classification_model\")\n", "# model = SetFitModel.from_pretrained(\"text_classification_model\")\n", "\n", "# Push and load in HF\n", "# model.push_to_hub(\"[username]/text_classification_model\")\n", "# model = SetFitModel.from_pretrained(\"[username]/text_classification_model\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It's time to make the predictions! We will set a function that uses the `predict` method to get the suggested label. The model will infer the label based on the text."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["def predict(model, input, labels):\n", "    model.labels = labels\n", "\n", "    prediction = model.predict([input])\n", "\n", "    return prediction[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To update the records, we will need to retrieve them from the server and update them with the new suggestions. The `id` will always need to be provided as it is the records' identifier to update a record and avoid creating a new one."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = dataset.records.to_list(flatten=True)\n", "updated_data = [\n", "    {\n", "        \"sentiment_label\": predict(model, sample[\"review\"], labels),\n", "        \"id\": sample[\"id\"],\n", "    }\n", "    for sample in data\n", "]\n", "dataset.records.log(records=updated_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Voilà! We have added the suggestions to the dataset, and they will appear in the UI marked with a ✨. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate with Extralit"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we can start the annotation process. Just open the dataset in the Extralit UI and start annotating the records. If the suggestions are correct, you can just click on `Submit`. Otherwise, you can select the correct label."]}, {"cell_type": "markdown", "metadata": {}, "source": ["!!! note\n", "    Check this [how-to guide](../admin_guide/annotate.md) to know more about annotating in the UI."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train your model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After the annotation, we will have a robust dataset to train the main model. In our case, we will fine-tune using SetFit. However, you can select the one that best fits your requirements. So, let's start by retrieving the annotated records.\n", "\n", "!!! note\n", "    Check this [how-to guide](../admin_guide/query.md) to know more about filtering and querying in Extralit. Also, you can check the Hugging Face docs on [fine-tuning an text classification model](https://huggingface.co/docs/transformers/en/tasks/sequence_classification)."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["dataset = client.datasets(\"text_classification_dataset\")"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["status_filter = ex.Query(filter=ex.Filter((\"response.status\", \"==\", \"submitted\")))\n", "\n", "submitted = dataset.records(status_filter).to_list(flatten=True)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As we have a single response per record, we can retrieve the selected label straightforwardly and create the training set with 8 samples per label. We selected 8 samples per label to have a balanced dataset for few-shot learning."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_records = [\n", "    {\n", "        \"text\": r[\"review\"],\n", "        \"label\": r[\"sentiment_label.responses\"][0],\n", "    }\n", "    for r in submitted\n", "]\n", "train_dataset = Dataset.from_list(train_records)\n", "train_dataset = sample_dataset(train_dataset, label_column=\"label\", num_samples=8)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can train the model using our previous function, but this time with a high-quality human-annotated training set."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = train_model(model_name=\"TaylorAI/bge-micro-v2\", dataset=train_dataset)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As the training data was of better quality, we can expect a better model. So we can update the remaining non-annotated records with the new model's suggestions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = dataset.records.to_list(flatten=True)\n", "updated_data = [\n", "    {\n", "        \"sentiment_label\": predict(model, sample[\"review\"], labels),\n", "        \"id\": sample[\"id\"],\n", "    }\n", "    for sample in data\n", "]\n", "dataset.records.log(records=updated_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this tutorial, we present an end-to-end example of a text classification task. This serves as the base, but it can be performed iteratively and seamlessly integrated into your workflow to ensure high-quality curation of your data and improved results.\n", "\n", "We started by configuring the dataset, adding records, and training a zero-shot SetFit model, as an example, to add suggestions. After the annotation process, we trained a new model with the annotated data and updated the remaining records with the new suggestions."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}