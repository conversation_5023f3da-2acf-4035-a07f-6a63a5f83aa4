/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'import': {
    width: 29,
    height: 31,
    viewBox: '0 0 29 31',
    data: '<path pid="0" d="M3.446 12.957v15.014h22.216V12.957h-6.348V9.955h9.521v21.019H.272V9.954h9.521v3.003H3.446z" _fill="#000" fill-opacity=".87"/><path pid="1" d="M16.14.974h-3.173V19.07l-4.024-3.807L6.7 17.386l7.855 7.431 7.854-7.431-2.244-2.123-4.023 3.807V.974z" _fill="#000" fill-opacity=".87"/>'
  }
})