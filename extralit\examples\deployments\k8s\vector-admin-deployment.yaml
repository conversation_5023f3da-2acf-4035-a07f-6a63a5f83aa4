apiVersion: apps/v1
kind: Deployment
metadata:
  name: vector-admin-deployment
  labels:
    app: vector-admin
spec:
  replicas: 1
  selector:
    matchLabels:
      app: vector-admin
  template:
    metadata:
      labels:
        app: vector-admin
    spec:
      containers:
      - name: vector-admin
        image: mintplexlabs/vectoradmin:latest
        ports:
        - containerPort: 3001
        env:
        - name: SERVER_PORT
          value: "3001"
        - name: INNGEST_EVENT_KEY
          value: "background_workers"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: vector-admin-secrets
              key: JWT_SECRET
        - name: INNGEST_SIGNING_KEY
          valueFrom:
            secretKeyRef:
              name: vector-admin-secrets
              key: INNGEST_SIGNING_KEY
        - name: DATABASE_CONNECTION_STRING
          valueFrom:
            secretKeyRef:
              name: vector-admin-secrets
              key: DATABASE_CONNECTION_STRING
        - name: INNGEST_LANDING_PAGE
          value: "false"