/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'validate': {
    width: 31,
    height: 31,
    viewBox: '0 0 31 31',
    data: '<path pid="0" fill-rule="evenodd" clip-rule="evenodd" d="M15.75.75c-8.284 0-15 6.716-15 15 0 8.284 6.716 15 15 15 8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15zm-11.826 15c0-6.531 5.295-11.826 11.826-11.826 6.531 0 11.826 5.295 11.826 11.826 0 6.531-5.295 11.826-11.826 11.826-6.531 0-11.826-5.295-11.826-11.826zm19.3-3.698L20.98 9.807l-7.587 7.587-3.794-3.793-2.244 2.244 6.038 6.039 9.832-9.832z" _fill="#000"/>'
  }
})