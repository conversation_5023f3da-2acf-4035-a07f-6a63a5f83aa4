{{- if .Values.extralit.ingress.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ include "extralit.fullname" . }}
  labels:
    {{- include "extralit.labels" . | nindent 4 }}
  {{- with .Values.extralit.ingress.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  ingressClassName: {{ .Values.extralit.ingress.className }}
  rules:
    - host: {{ .Values.extralit.ingress.host | quote }}
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: {{ include "extralit.fullname" . }}
                port:
                  number: 6900
{{- end }}