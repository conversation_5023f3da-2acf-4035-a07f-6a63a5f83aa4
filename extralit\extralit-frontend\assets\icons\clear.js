/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'clear': {
    width: 18,
    height: 18,
    viewBox: '0 0 18 18',
    data: '<path pid="0" fill-rule="evenodd" clip-rule="evenodd" d="M1.02 9.845a1.878 1.878 0 000 2.655l3.622 3.622H1.569a.939.939 0 000 1.878h15.023a.939.939 0 000-1.878H8.02l8.933-8.933a1.878 1.878 0 000-2.656L12.97.55a1.878 1.878 0 00-2.656 0L1.019 9.845zm4.06-1.406l-2.733 2.733 3.984 3.984 2.733-2.734-3.983-3.983zM6.41 7.11l3.983 3.983 5.233-5.233-3.983-3.983L6.409 7.11z" _fill="#52A3ED"/>'
  }
})