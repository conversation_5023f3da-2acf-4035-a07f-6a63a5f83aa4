/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'refresh': {
    width: 40,
    height: 40,
    viewBox: '0 0 40 40',
    data: '<path pid="0" d="M33.861 17.47L30.521 5 26.98 8.54C20.5 4.458 11.836 5.238 6.193 10.882l2.635 2.635c4.178-4.178 10.485-4.93 15.429-2.253l-2.865 2.864 12.47 3.341zM6.139 22.53L9.479 35l3.541-3.54c6.48 4.082 15.144 3.302 20.787-2.342l-2.635-2.635c-4.178 4.178-10.485 4.93-15.429 2.253l2.865-2.864-12.47-3.341z" _fill="#000"/>'
  }
})