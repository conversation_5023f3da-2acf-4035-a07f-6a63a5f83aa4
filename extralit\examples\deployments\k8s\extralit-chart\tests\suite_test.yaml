suite: test extralit chart
templates:
  - templates/configmap.yaml
  - templates/deployment.yaml
  - templates/elasticsearch-operator.yaml
  - templates/pvc.yaml
  - templates/service.yaml
  - templates/hpa.yaml
  - templates/ingress.yaml

tests:
  - it: should render configmap when elasticsearch.useOperator is false
    set:
      elasticsearch.useOperator: false
      configmap.minikubeIP: "************"
      ingress.host: "extralit.local"
    templates:
      - templates/configmap.yaml
    asserts:
      - isKind:
          of: ConfigMap
      - equal:
          path: metadata.name
          value: custom-hosts
      - matchRegex:
          path: data.hosts
          pattern: ************ extralit.local

  - it: should not render configmap when elasticsearch.useOperator is true
    set:
      elasticsearch.useOperator: true
    templates:
      - templates/configmap.yaml
    asserts:
      - hasDocuments:
          count: 0

  - it: should render deployment correctly
    set:
      extralit.replicaCount: 2
      extralit.image.repository: extralit/extralit
      extralit.image.tag: latest
      extralit.authSecretKey: testsecret
      extralit.auth.username: extralit
      extralit.auth.password: "12345678"
    templates:
      - templates/deployment.yaml
    asserts:
      - isKind:
          of: Deployment
      - equal:
          path: spec.replicas
          value: 2
      - equal:
          path: spec.template.spec.containers[0].image
          value: extralit/extralit:latest
      - contains:
          path: spec.template.spec.containers[0].env
          content:
            name: PASSWORD
            value: "12345678"

  - it: should render elasticsearch operator resources when enabled
    set:
      elasticsearch.useOperator: true
      elasticsearch.version: 7.17.0
      elasticsearch.nodeCount: 3
      elasticsearch.persistence.storage.size: 10Gi
    templates:
      - templates/elasticsearch-operator.yaml
    asserts:
      - isKind:
          of: Elasticsearch
      - equal:
          path: spec.version
          value: 7.17.0
      - equal:
          path: spec.nodeSets[0].count
          value: 3
      - equal:
          path: spec.nodeSets[0].volumeClaimTemplates[0].spec.resources.requests.storage
          value: 10Gi

  - it: should render PVC when persistence is enabled
    set:
      extralit.persistence.enabled: true
      extralit.persistence.accessMode: ReadWriteOnce
      extralit.persistence.size: 5Gi
    templates:
      - templates/pvc.yaml
    asserts:
      - isKind:
          of: PersistentVolumeClaim
      - equal:
          path: spec.accessModes[0]
          value: ReadWriteOnce
      - equal:
          path: spec.resources.requests.storage
          value: 5Gi
      - notHasKey: spec.storageClassName

  - it: should render service correctly
    templates:
      - templates/service.yaml
    asserts:
      - isKind:
          of: Service
      - equal:
          path: spec.ports[0].port
          value: 6900
      - equal:
          path: spec.ports[0].name
          value: http

  - it: should render HPA when enabled
    set:
      extralit.hpa.enabled: true
      extralit.hpa.minReplicas: 2
      extralit.hpa.maxReplicas: 5
      extralit.hpa.targetCPUUtilizationPercentage: 80
    templates:
      - templates/hpa.yaml
    asserts:
      - isKind:
          of: HorizontalPodAutoscaler
      - equal:
          path: spec.minReplicas
          value: 2
      - equal:
          path: spec.maxReplicas
          value: 5
      - equal:
          path: spec.metrics[0].resource.target.averageUtilization
          value: 80
      - equal:
          path: spec.scaleTargetRef.kind
          value: Deployment
      - equal:
          path: apiVersion
          value: autoscaling/v2

  - it: should not render HPA when disabled
    set:
      extralit.hpa.enabled: false
    templates:
      - templates/hpa.yaml
    asserts:
      - hasDocuments:
          count: 0

  - it: should render Ingress when enabled
    set:
      extralit.ingress.enabled: true
      extralit.ingress.className: nginx
      extralit.ingress.host: extralit.example.com
      extralit.ingress.annotations:
        kubernetes.io/ingress.class: nginx
    templates:
      - templates/ingress.yaml
    asserts:
      - isKind:
          of: Ingress
      - equal:
          path: spec.ingressClassName
          value: nginx
      - equal:
          path: spec.rules[0].host
          value: extralit.example.com
      - equal:
          path: spec.rules[0].http.paths[0].path
          value: /
      - equal:
          path: spec.rules[0].http.paths[0].pathType
          value: Prefix
      - equal:
          path: spec.rules[0].http.paths[0].backend.service.port.number
          value: 6900
      - equal:
          path: metadata.annotations["kubernetes.io/ingress.class"]
          value: nginx

  - it: should not render Ingress when disabled
    set:
      extralit.ingress.enabled: false
    templates:
      - templates/ingress.yaml
    asserts:
      - hasDocuments:
          count: 0
