name: "🐞 Bug report: UI/UX"
description: UI or UX bugs and unexpected behavior
title: "[BUG-UI/UX]"
labels: ["bug", "ui/ux"]
assignees: []
type: "Bug"
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this UI/UX bug report!

  - type: textarea
    id: description
    attributes:
      label: What happened?
      description: Also tell us, what did you expect to happen?
      placeholder: I encountered an issue when...
      value: "A bug happened!"
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem.
      placeholder: Drag and drop images here

  - type: textarea
    id: reproduce
    attributes:
      label: Steps to reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - Windows
        - macOS
        - Linux
        - iOS
        - Android
        - Other
    validations:
      required: true

  - type: dropdown
    id: browsers
    attributes:
      label: Browsers
      description: What browsers are you seeing the problem on?
      multiple: true
      options:
        - Chrome
        - Firefox
        - Safari
        - Edge
        - Opera
        - Other
    validations:
      required: true

  - type: input
    id: extralit-version
    attributes:
      label: Extralit Version
      description: What version of Extralit are you using?
      placeholder: e.g. 1.0.0
    validations:
      required: true

  - type: input
    id: elasticsearch-version
    attributes:
      label: ElasticSearch Version
      description: What version of ElasticSearch are you using?
      placeholder: e.g. 7.10.2
    validations:
      required: false

  - type: input
    id: docker-image
    attributes:
      label: Docker Image (optional)
      description: Which Docker image are you using?
      placeholder: e.g. extralit:v1.0.0

  - type: textarea
    id: additional
    attributes:
      label: Additional context
      description: Add any other context about the problem here.
      placeholder: Any other information that might be helpful...
