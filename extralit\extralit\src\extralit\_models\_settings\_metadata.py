# Copyright 2024-present, Extralit Labs, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

from typing import Annotated, Any, Literal, Optional, Union
from uuid import UUID

from pydantic import BaseModel, Field, field_serializer, field_validator, model_validator

from extralit._exceptions import MetadataError
from extralit._models import ResourceModel


class BaseMetadataPropertySettings(BaseModel):
    visible_for_annotators: Optional[bool] = True


class TermsMetadataPropertySettings(BaseMetadataPropertySettings):
    type: Literal["terms"] = "terms"
    values: Optional[list[Any]] = None

    @field_validator("values")
    @classmethod
    def __validate_values(cls, values):
        if values is None:
            return None
        if not isinstance(values, list):
            raise ValueError(f"values must be a list, got {type(values)}")
        return values


class NumericMetadataPropertySettings(BaseMetadataPropertySettings):
    min: Optional[Union[int, float]] = None
    max: Optional[Union[int, float]] = None

    @model_validator(mode="before")
    @classmethod
    def __validate_min_max(cls, values):
        min_value = values.get("min")
        max_value = values.get("max")

        if min_value is not None and max_value is not None:
            if min_value >= max_value:
                raise MetadataError("min must be less than max.")
        return values


class IntegerMetadataPropertySettings(NumericMetadataPropertySettings):
    type: Literal["integer"] = "integer"

    @model_validator(mode="before")
    @classmethod
    def __validate_min_max(cls, values):
        min_value = values.get("min")
        max_value = values.get("max")

        if not all(isinstance(value, int) or value is None for value in [min_value, max_value]):
            raise MetadataError("min and max must be integers.")
        return values


class FloatMetadataPropertySettings(NumericMetadataPropertySettings):
    type: Literal["float"] = "float"


MetadataPropertySettings = Annotated[
    Union[
        TermsMetadataPropertySettings,
        IntegerMetadataPropertySettings,
        FloatMetadataPropertySettings,
    ],
    Field(..., discriminator="type"),
]


class MetadataFieldModel(ResourceModel):
    """The schema definition of a metadata field in an Extralit dataset."""

    name: str
    settings: MetadataPropertySettings

    title: Optional[str] = None
    visible_for_annotators: Optional[bool] = True

    dataset_id: Optional[UUID] = None

    @property
    def type(self) -> str:
        return self.settings.type

    @field_validator("title")
    @classmethod
    def __title_default(cls, title, values):
        validated_title = title or values.data["name"]
        return validated_title

    @field_serializer("id", "dataset_id", when_used="unless-none")
    def serialize_id(self, value: UUID) -> str:
        return str(value)
