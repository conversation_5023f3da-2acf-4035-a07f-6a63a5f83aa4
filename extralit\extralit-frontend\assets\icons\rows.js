/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'rows': {
    width: 10,
    height: 8,
    viewBox: '0 0 10 8',
    data: '<path pid="0" fill-rule="evenodd" clip-rule="evenodd" d="M8.68.7H1a.3.3 0 00-.3.3v1.421h8.28V1a.3.3 0 00-.3-.3zM.7 4.879V3.12h8.28v1.76L.7 4.879zm0 .7V7a.3.3 0 00.3.3h7.68a.3.3 0 00.3-.3V5.579H.7zM1 0a1 1 0 00-1 1v6a1 1 0 001 1h7.68a1 1 0 001-1V1a1 1 0 00-1-1H1z" _fill="#000" fill-opacity=".54"/>'
  }
})