/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'trash-empty': {
    width: 41,
    height: 40,
    viewBox: '0 0 41 40',
    data: '<path pid="0" fill-rule="evenodd" clip-rule="evenodd" d="M12.605 9.737v-1.58A3.158 3.158 0 0115.763 5h9.474a3.158 3.158 0 013.158 3.158v1.579h4.737a1.579 1.579 0 010 3.158h-1.58v17.368A4.737 4.737 0 0126.817 35H14.184a4.737 4.737 0 01-4.737-4.737V12.895H7.868a1.579 1.579 0 110-3.158h4.737zm3.158-1.58h9.474v1.58h-9.474v-1.58zm-3.158 4.738h15.79v17.368c0 .872-.707 1.58-1.58 1.58h-12.63a1.579 1.579 0 01-1.58-1.58V12.895z" _fill="#000"/>'
  }
})