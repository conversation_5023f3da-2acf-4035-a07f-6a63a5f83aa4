{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Image classification"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- **Goal**: Show a standard workflow for an image classification task.\n", "- **Dataset**: [MNIST](https://huggingface.co/datasets/ylecun/mnist), a dataset of 28x28 grayscale images that need to be classified as digits.\n", "- **Libraries**: [datasets](https://github.com/huggingface/datasets), [transformers](https://github.com/huggingface/transformers)\n", "- **Components**: [ImageField](https://docs.argilla.io/latest/reference/argilla/settings/fields/#src.argilla.settings._field.ImageField), [LabelQuestion](https://docs.argilla.io/latest/reference/argilla/settings/questions/#src.argilla.settings._question.LabelQuestion), [Suggestion](https://docs.argilla.io/latest/reference/argilla/records/suggestions/)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Getting started"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Deploy the Extralit server"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you already have deployed Extralit, you can skip this step. Otherwise, you can quickly deploy Extralit following [this guide](../getting_started/quickstart.md)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Set up the environment"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To complete this tutorial, you need to install the Extralit SDK and a few third-party libraries via `pip`."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install extralit"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!pip install \"transformers[torch]~=4.0\" \"accelerate~=0.34\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's make the required imports:"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import torch\n", "from datasets import Dataset, load_dataset, load_metric\n", "from IPython.display import display\n", "from PIL import Image\n", "from transformers import AutoImageProcessor, AutoModelForImageClassification, Trainer, TrainingArguments, pipeline\n", "\n", "import extralit as ex"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You also need to connect to the Extralit server using the `api_url` and `api_key`."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Replace api_url with your url if using Docker\n", "# Replace api_key with your API key under \"My Settings\" in the UI\n", "# Uncomment the last line and set your HF_TOKEN if your space is private\n", "client = ex.Extralit(\n", "    api_url=\"https://[your-owner-name]-[your_space_name].hf.space\",\n", "    api_key=\"[your-api-key]\",\n", "    # headers={\"Authorization\": f\"Bearer {HF_TOKEN}\"}\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Vibe check the dataset\n", "\n", "We will look at [the dataset](https://huggingface.co/datasets/ylecun/mnist) to understand its structure and the kind of data it contains. We do this by using [the embedded Hugging Face Dataset Viewer](https://huggingface.co/docs/hub/main/en/datasets-viewer-embed).\n", "\n", "<iframe\n", "  src=\"https://huggingface.co/datasets/ylecun/mnist/embed/viewer/mnist/train\"\n", "  frameborder=\"0\"\n", "  width=\"100%\"\n", "  height=\"560px\"\n", "></iframe>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configure and create the Extralit dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we will need to configure the dataset. In the settings, we can specify the guidelines, fields, and questions. If needed, you can also add metadata and vectors. However, for our use case, we just need a field for the `image` column and a label question for the `label` column.\n", "\n", "!!! note\n", "    Check this [how-to guide](../admin_guide/dataset.md) to know more about configuring and creating a dataset."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["labels = [str(x) for x in range(10)]\n", "\n", "settings = ex.Settings(\n", "    guidelines=\"The goal of this task is to classify a given image of a handwritten digit into one of 10 classes representing integer values from 0 to 9, inclusively.\",\n", "    fields=[\n", "        ex.ImageField(\n", "            name=\"image\",\n", "            title=\"An image of a handwritten digit.\",\n", "        ),\n", "    ],\n", "    questions=[\n", "        ex.LabelQuestion(\n", "            name=\"image_label\",\n", "            title=\"What digit do you see on the image?\",\n", "            labels=labels,\n", "        )\n", "    ],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's create the dataset with the name and the defined settings:"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["dataset = ex.Dataset(\n", "    name=\"image_classification_dataset\",\n", "    settings=settings,\n", ")\n", "dataset.create()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Add records"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Even if we have created the dataset, it still lacks the information to be annotated (you can check it in the UI). We will use the `ylecun/mnist` dataset from [the Hugging Face Hub](https://huggingface.co/datasets/ylecun/mnist). Specifically, we will use `100` examples. Because we are dealing with a potentially large image dataset, we will set `streaming=True` to avoid loading the entire dataset into memory and iterate over the data to lazily load it.\n", "\n", "!!! tip\n", "    When working with Hugging Face datasets, you can set `Image(decode=False)` so that we can get [public image URLs](https://huggingface.co/docs/datasets/en/image_load#local-files), but this depends on the dataset."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset({\n", "    features: ['image', 'label'],\n", "    num_rows: 100\n", "})"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["n_rows = 100\n", "\n", "hf_dataset = load_dataset(\"ylecun/mnist\", streaming=True)\n", "dataset_rows = [row for _, row in zip(range(n_rows), hf_dataset[\"train\"])]\n", "hf_dataset = Dataset.from_list(dataset_rows)\n", "\n", "hf_dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Let's have a look at the first image in the dataset."]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'image': <PIL.PngImagePlugin.PngImageFile image mode=L size=28x28>,\n", " 'label': 5}"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["hf_dataset[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will easily add them to the dataset using `log`, without needing a mapping since the names already match the Extralit resources. Additionally, since the images are already in PIL format and defined as `Image` in the Hugging Face dataset’s features, we can log them directly. We will also include an `id` column in each record, allowing us to easily trace back to the external data source."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hf_dataset = hf_dataset.add_column(\"id\", range(len(hf_dataset)))\n", "dataset.records.log(records=hf_dataset)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Add initial model suggestions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The next step is to add suggestions to the dataset. This will make things easier and faster for the annotation team. Suggestions will appear as preselected options, so annotators will only need to correct them. In our case, we will generate them using [a zero-shot CLIP model](https://huggingface.co/docs/transformers/en/tasks/zero_shot_image_classification). However, you can use a framework or technique of your choice."]}, {"cell_type": "markdown", "metadata": {}, "source": ["We will start by loading the model using a `transformers` pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkpoint = \"openai/clip-vit-large-patch14\"\n", "detector = pipeline(model=checkpoint, task=\"zero-shot-image-classification\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, let's try to make a model prediction and see if it makes sense."]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"image/jpeg": "/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAgGBgcGBQgHBwcJCQgKDBQNDAsLDBkSEw8UHRofHh0aHBwgJC4nICIsIxwcKDcpLDAxNDQ0Hyc5PTgyPC4zNDL/wAALCAAcABwBAREA/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/9oACAEBAAA/APn+trwt4W1TxfrcOl6VDvlc5eRvuRL3Zj2A/wD1V0Xj74YXvgeKG5S+TU7NmMU00MRUW8oCnY/JAzu455x0FcHVnTrC51XUrbT7OMyXNzKsUaDuxOBXrmveMYfhfpk/gfwsG/tCJ1e/1TOC8pwWVR2GMLnIxg9+aboGsX/jvwD8SLrX7tpXjitrqPaAio6iTGAOMHYo98eteOV0ngC8t7D4gaDd3cyQ28V5G0kjnCqM9SfSvRvFXwjgttb1LxDr/i6xs9Hup3uY5QpeaYMxbaqjAJwe2fpXDeJ/Fdk+nHw34Whez8PKweTzADNeSA/6yRuuPReAPT046iiiiv/Z", "image/png": "iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAAAAABXZoBIAAABDklEQVR4AWNgGHhgPP/vfCMczjB49+fPn7fYJc0e//3z/uUfSzZMaS6bB3/+/jkV8udvFUSSCUnNzAMyQJ4Rz0EGXQxJY29GxkOljC/OT2JiRNICZoLcspnHu1KUgeHvZzQHqy39+/JCCETH3z9LUbSyb/rzwV0YZCcQ/P1zGMKAkpZ//tjDBdAlj/3dB5dj+P/3CJgD9YqPwf9NCMl//y8gOAwMoX+eScL47O1/d/HAOCA69M99GJe9+c9DdxgHTIf+mQjlGyz9sxZFioEh7O9DiEjRu7+L0OSAxv6cZCAbuunh3/vLLTAl//x5eh0Yl0ea0KUYGGSO/wHG1p+XMJtRVUg2ACV7VVEFB4IHAKxwbkRtVspVAAAAAElFTkSuQmCC", "text/plain": ["<PIL.PngImagePlugin.PngImageFile image mode=L size=28x28>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["([{'score': 0.5236628651618958, 'label': '0'},\n", "  {'score': 0.11496700346469879, 'label': '7'},\n", "  {'score': 0.08030630648136139, 'label': '8'},\n", "  {'score': 0.07141078263521194, 'label': '9'},\n", "  {'score': 0.05868939310312271, 'label': '6'},\n", "  {'score': 0.05507850646972656, 'label': '5'},\n", "  {'score': 0.0341767854988575, 'label': '1'},\n", "  {'score': 0.027202051132917404, 'label': '4'},\n", "  {'score': 0.018533246591687202, 'label': '3'},\n", "  {'score': 0.015973029658198357, 'label': '2'}],\n", " None)"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["predictions = detector(hf_dataset[1][\"image\"], candidate_labels=labels)\n", "predictions, display(hf_dataset[1][\"image\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["It's time to make the predictions on the dataset! We will set a function that uses the zero-shot model. The model will infer the label based on the image. When working with large datasets, you can create a `batch_predict` method to speed up the process."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["def predict(input, labels):\n", "    prediction = detector(input, candidate_labels=labels)\n", "    prediction = prediction[0]\n", "    return {\"image_label\": prediction[\"label\"], \"score\": prediction[\"score\"]}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["To update the records, we will need to retrieve them from the server and update them with the new suggestions. The `id` will always need to be provided as it is the records' identifier to update a record and avoid creating a new one."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = dataset.records.to_list(flatten=True)\n", "updated_data = [\n", "    {\n", "        \"id\": sample[\"id\"],\n", "        **predict(sample[\"image\"], labels),\n", "    }\n", "    for sample in data\n", "]\n", "dataset.records.log(records=updated_data, mapping={\"score\": \"image_label.suggestion.score\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Voilà! We have added the suggestions to the dataset, and they will appear in the UI marked with a ✨. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate with Extralit"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now, we can start the annotation process. Just open the dataset in the Extralit UI and start annotating the records. If the suggestions are correct, you can just click on `Submit`. Otherwise, you can select the correct label."]}, {"cell_type": "markdown", "metadata": {}, "source": ["!!! note\n", "    Check this [how-to guide](../admin_guide/annotate.md) to know more about annotating in the UI."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train your model"]}, {"cell_type": "markdown", "metadata": {}, "source": ["After the annotation, we will have a robust dataset to train the main model. In our case, we will fine-tune using transformers. However, you can select the one that best fits your requirements."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Formatting the data"]}, {"cell_type": "markdown", "metadata": {}, "source": ["So, let's start by retrieving the annotated records and exporting them as a `Dataset`, so images will be in PIL format.\n", "\n", "!!! note\n", "    Check this [how-to guide](../admin_guide/query.md) to know more about filtering and querying in Extralit. Also, you can check the Hugging Face docs on [fine-tuning an image classification model](https://huggingface.co/docs/transformers/en/tasks/image_classification)."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["dataset = client.datasets(\"image_classification_dataset\")"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["status_filter = ex.Query(filter=ex.Filter((\"response.status\", \"==\", \"submitted\")))\n", "\n", "submitted = dataset.records(status_filter).to_datasets()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We now need to ensure our images are forwarded with the correct dimensions. Because the original MNIST dataset is greyscale and the VIT model expects RGB, we need to add a channel dimension to the images. We will do this by stacking the images along the channel axis."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'id': '0', 'image': <PIL.Image.Image image mode=RGB size=28x28>, 'label': '0'}"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["def greyscale_to_rgb(img) -> Image:\n", "    return Image.merge(\"RGB\", (img, img, img))\n", "\n", "\n", "submitted_image_rgb = [\n", "    {\n", "        \"id\": sample[\"id\"],\n", "        \"image\": greyscale_to_rgb(sample[\"image\"]),\n", "        \"label\": sample[\"image_label.responses\"][0],\n", "    }\n", "    for sample in submitted\n", "]\n", "submitted_image_rgb[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we will load the `ImageProcessor` to fine-tune the model. This processor will handle the image resizing and normalization in order to be compatible with the model we intend to use."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["checkpoint = \"google/vit-base-patch16-224-in21k\"\n", "processor = AutoImageProcessor.from_pretrained(checkpoint)\n", "\n", "submitted_image_rgb_processed = [\n", "    {\n", "        \"pixel_values\": processor(sample[\"image\"], return_tensors=\"pt\")[\"pixel_values\"],\n", "        \"label\": sample[\"label\"],\n", "    }\n", "    for sample in submitted_image_rgb\n", "]\n", "submitted_image_rgb_processed[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We can now convert the images to a Hugging Face Dataset that is ready for fine-tuning."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["DatasetDict({\n", "    train: Dataset({\n", "        features: ['pixel_values', 'label'],\n", "        num_rows: 80\n", "    })\n", "    test: Dataset({\n", "        features: ['pixel_values', 'label'],\n", "        num_rows: 20\n", "    })\n", "})"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["prepared_ds = Dataset.from_list(submitted_image_rgb_processed)\n", "prepared_ds = prepared_ds.train_test_split(test_size=0.2)\n", "prepared_ds"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### The actual training"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We then need to define our data collator, which will ensure the data is unpacked and stacked correctly for the model."]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def collate_fn(batch):\n", "    return {\n", "        \"pixel_values\": torch.stack([torch.tensor(x[\"pixel_values\"][0]) for x in batch]),\n", "        \"labels\": torch.tensor([int(x[\"label\"]) for x in batch]),\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Next, we can define our training metrics. We will use the accuracy metric to evaluate the model's performance."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["metric = load_metric(\"accuracy\", trust_remote_code=True)\n", "\n", "\n", "def compute_metrics(p):\n", "    return metric.compute(predictions=np.argmax(p.predictions, axis=1), references=p.label_ids)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We then load our model and configure the labels that we will use for training."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["model = AutoModelForImageClassification.from_pretrained(\n", "    checkpoint,\n", "    num_labels=len(labels),\n", "    id2label={int(i): int(c) for i, c in enumerate(labels)},\n", "    label2id={int(c): int(i) for i, c in enumerate(labels)},\n", ")\n", "model.config"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Finally, we define the training arguments and start the training process."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'train_runtime': 12.5374, 'train_samples_per_second': 6.381, 'train_steps_per_second': 0.399, 'train_loss': 2.0533515930175783, 'epoch': 1.0}\n", "***** train metrics *****\n", "  epoch                    =        1.0\n", "  total_flos               =  5774017GF\n", "  train_loss               =     2.0534\n", "  train_runtime            = 0:00:12.53\n", "  train_samples_per_second =      6.381\n", "  train_steps_per_second   =      0.399\n"]}], "source": ["training_args = TrainingArguments(\n", "    output_dir=\"./image-classifier\",\n", "    per_device_train_batch_size=16,\n", "    eval_strategy=\"steps\",\n", "    num_train_epochs=1,\n", "    fp16=False,  # True if you have a GPU with mixed precision support\n", "    save_steps=100,\n", "    eval_steps=100,\n", "    logging_steps=10,\n", "    learning_rate=2e-4,\n", "    save_total_limit=2,\n", "    remove_unused_columns=True,\n", "    push_to_hub=False,\n", "    load_best_model_at_end=True,\n", ")\n", "\n", "trainer = Trainer(\n", "    model=model,\n", "    args=training_args,\n", "    data_collator=collate_fn,\n", "    compute_metrics=compute_metrics,\n", "    train_dataset=prepared_ds[\"train\"],\n", "    eval_dataset=prepared_ds[\"test\"],\n", "    tokenizer=processor,\n", ")\n", "\n", "train_results = trainer.train()\n", "trainer.save_model()\n", "trainer.log_metrics(\"train\", train_results.metrics)\n", "trainer.save_metrics(\"train\", train_results.metrics)\n", "trainer.save_state()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["As the training data was of better quality, we can expect a better model. So we can update the remainder of our original dataset with the new model's suggestions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pipe = pipeline(\"image-classification\", model=model, image_processor=processor)\n", "\n", "\n", "def run_inference(batch):\n", "    predictions = pipe(batch[\"image\"])\n", "    batch[\"image_label\"] = [prediction[0][\"label\"] for prediction in predictions]\n", "    batch[\"score\"] = [prediction[0][\"score\"] for prediction in predictions]\n", "    return batch\n", "\n", "\n", "hf_dataset = hf_dataset.map(run_inference, batched=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["data = dataset.records.to_list(flatten=True)\n", "updated_data = [\n", "    {\n", "        \"image_label\": str(sample[\"image_label\"]),\n", "        \"id\": sample[\"id\"],\n", "        \"score\": sample[\"score\"],\n", "    }\n", "    for sample in hf_dataset\n", "]\n", "dataset.records.log(records=updated_data, mapping={\"score\": \"image_label.suggestion.score\"})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusions"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this tutorial, we present an end-to-end example of an image classification task. This serves as the base, but it can be performed iteratively and seamlessly integrated into your workflow to ensure high-quality curation of your data and improved results.\n", "\n", "We started by configuring the dataset and adding records and suggestions from a zero-shot model. After the annotation process, we trained a new model with the annotated data and updated the remaining records with the new suggestions."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.4"}}, "nbformat": 4, "nbformat_minor": 2}