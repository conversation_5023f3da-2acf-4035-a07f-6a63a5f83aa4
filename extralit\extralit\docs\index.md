---
description: Extralit is a collaboration tool for AI engineers and domain experts to build high-quality datasets.
hide: navigation
---

# Overview

Extralit is an advanced literature review tool designed for efficient data extraction from scientific literature. It leverages LLM technology and RAG techniques to assist researchers in extracting structured data according to predefined Schemas.

To get started:

<div class="grid cards" markdown>

-  __Get started in 5 minutes!__

    ---

    Deploy Extralit for free on the Hugging Face Hub or with `Docker`. Install the Python SDK with `pip` and create your first project.

    [:octicons-arrow-right-24: Quickstart](getting_started/quickstart.md)

-  __How-to guides__

    ---

    Get familiar with the basic workflows of Extralit. Learn how to manage `Users`, `Workspaces`, `Datasets`, and `Records` to set up your data annotation projects.

    [:octicons-arrow-right-24: Learn more](user_guide/index.md)

</div>

Or, play with the Extralit UI by signing in with your Hugging Face account on the [public HuggingFace Space deployment](https://extralit-public-demo.hf.space/welcome-hf-sign-in/).

## Why use Extralit?

Extralit is designed to helps researchers and data scientists tackle the challenges of processing large volumes of academic papers, ensuring **high-quality data extraction for scientific analysis and meta-studies**. By combining LLMs and advanced ML models with intuitive workflows, Extralit is a powerful tool designed to transform unstructured scientific papers into structured, analyzable data.

<p style="font-size:20px">Accelerate scientific data extraction</p>

Manual data extraction from scientific papers is time-consuming and error-prone. Extralit leverages LLMs to **automate and assist in the extraction process**, significantly reducing the time and effort required to compile structured datasets from literature.

<p style="font-size:20px">Ensure consistency and accuracy in extracted data</p>

Scientific research demands high-quality, complete, and consistent data, but every field of study has unique data requirements. Extralit offers **customizable extraction schemas and workflows to validate and review data**, allowing you to define exactly the size and format of data extracted and how it should be structured, adapting to the specific needs of your research domain.

<p style="font-size:20px">Advanced scientific text and table parsing</p>
Scientific papers come in a variety of formats and layouts, often with complex tables and figures. Extralit excels at handling diverse scientific paper formats, employing advanced parsing techniques to ensure you get complete and accurate data, regardless of the complexity of the source material.

<p style="font-size:20px">Collaborate effectively on large-scale literature reviews</p>

Literature review and meta-analysis projects often require team effort. Extralit builds upon Extralit's platform to facilitate **collaborative extraction**, allowing multiple researchers to work together efficiently, share insights, and maintain a consistent approach across large volumes of literature.


## Relationship to Extralit
Extralit builds upon [Argilla's](https://argilla.io) foundation, adding specialized features for scientific data extraction.
