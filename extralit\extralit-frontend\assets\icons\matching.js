/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'matching': {
    width: 40,
    height: 40,
    viewBox: '0 0 40 40',
    data: '<path pid="0" d="M17.604 25.882l-5.786-5.785 1.929-1.929 3.857 3.857 7.713-7.713 1.929 1.928-9.642 9.642z" _fill="#000"/><path pid="1" fill-rule="evenodd" clip-rule="evenodd" d="M5 20c0-8.284 6.716-15 15-15 8.284 0 15 6.716 15 15 0 8.284-6.716 15-15 15-8.284 0-15-6.716-15-15zm15 12.273c-6.778 0-12.273-5.495-12.273-12.273S13.222 7.727 20 7.727 32.273 13.222 32.273 20 26.778 32.273 20 32.273z" _fill="#000"/>'
  }
})