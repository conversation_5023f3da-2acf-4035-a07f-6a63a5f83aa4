/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'exploration': {
    width: 40,
    height: 40,
    viewBox: '0 0 40 40',
    data: '<path pid="0" fill-rule="evenodd" clip-rule="evenodd" d="M25.163 20a5.163 5.163 0 11-10.326 0 5.163 5.163 0 0110.326 0zm-2.582 0a2.581 2.581 0 11-5.162 0 2.581 2.581 0 015.162 0z" _fill="#000"/><path pid="1" fill-rule="evenodd" clip-rule="evenodd" d="M20 8.384c7.217 0 13.28 4.936 15 11.616-1.72 6.68-7.783 11.616-15 11.616S6.72 26.68 5 20c1.72-6.68 7.783-11.616 15-11.616zm0 20.65c-5.776 0-10.672-3.796-12.316-9.034 1.644-5.238 6.54-9.035 12.316-9.035 5.776 0 10.672 3.797 12.316 9.035-1.644 5.238-6.54 9.035-12.316 9.035z" _fill="#000"/>'
  }
})