/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'row-last': {
    width: 40,
    height: 40,
    viewBox: '0 0 40 40',
    data: '<g opacity=".5" _fill="#000"><path pid="0" d="M7.143 22.143a2.143 2.143 0 010-4.286h17.143a2.143 2.143 0 110 4.286H7.143zM7.143 13.571a2.143 2.143 0 010-4.285h17.143a2.143 2.143 0 110 4.285H7.143z"/></g><path pid="1" d="M5 28.571c0 1.184.96 2.143 2.143 2.143h25.714a2.143 2.143 0 100-4.285H7.143C5.959 26.428 5 27.387 5 28.57z" _fill="#000"/>'
  }
})