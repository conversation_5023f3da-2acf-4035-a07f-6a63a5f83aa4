{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"scrolled": true}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/conda/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import json\n", "\n", "import extralit as ex\n", "\n", "client = ex.Extralit(api_url=\"http://localhost:6900/\", api_key=\"extralit.apikey\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["for dataset in client.datasets.list():\n", "    print(dataset.name)\n", "    # dataset.delete()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load extraction dataset"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["Dataset(id=UUID('3a7abf40-a6b7-4cf6-ac09-d89a8b33ac67') inserted_at=datetime.datetime(2024, 4, 4, 5, 23, 44, 562080) updated_at=datetime.datetime(2024, 11, 15, 0, 35, 14, 753190) name='2-Data-Extractions' status='ready' guidelines=None allow_extra_metadata=True distribution=OverlapTaskDistributionModel(strategy='overlap', min_submitted=1) workspace_id=UUID('e9e4e699-a6f9-4482-b5dd-e45874bd87eb') last_activity_at=datetime.datetime(2024, 12, 1, 5, 55, 8, 469548))"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset = client.datasets(name=\"2-Data-Extractions\", workspace=\"itn-recalibration\")\n", "dataset"]}, {"cell_type": "markdown", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["## Update field"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'reference': 'mosqueira2015pilot', 'pmid': '25959771', 'doc_id': '276c32ef-26d2-40cb-b808-b764018cd2ea', 'type': 'Observation'}\n", "<class 'str'>\n", "<class 'dict'>\n", "{'reference': 'mosqueira2015pilot', 'pmid': '25959771', 'doc_id': '276c32ef-26d2-40cb-b808-b764018cd2ea', 'type': 'ITNCondition'}\n", "<class 'str'>\n", "<class 'dict'>\n", "{'reference': 'mosqueira2015pilot', 'pmid': '25959771', 'doc_id': '276c32ef-26d2-40cb-b808-b764018cd2ea', 'type': 'EntomologicalOutcome'}\n", "<class 'str'>\n", "<class 'dict'>\n", "{'reference': 'mosqueira2015pilot', 'pmid': '25959771', 'doc_id': '276c32ef-26d2-40cb-b808-b764018cd2ea', 'type': 'ClinicalOutcome'}\n", "<class 'str'>\n", "<class 'dict'>\n"]}, {"data": {"text/plain": ["4"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# Find the record with the specific metadata\n", "records = dataset.records(\n", "    query=ex.Query(filter=(\"metadata.reference\", \"==\", \"mosqueira2015pilot\"))\n", ")\n", "\n", "# Update the record's extraction field\n", "updated_records = []\n", "for record in records:\n", "    print(record.metadata)\n", "    print(type(record.fields[\"extraction\"]))\n", "    record.fields[\"extraction\"] = json.loads(record.fields[\"extraction\"])\n", "    print(type(record.fields[\"extraction\"]))\n", "    updated_records.append(record)\n", "\n", "len(updated_records)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">DatasetRecords: The provided batch size <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">256</span> was normalized. Using value <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>.\n", "</pre>\n"], "text/plain": ["DatasetRecords: The provided batch size \u001b[1;36m256\u001b[0m was normalized. Using value \u001b[1;36m4\u001b[0m.\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Sending records...: 100%|███████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████████| 1/1 [00:04<00:00,  4.41s/batch]\n"]}, {"data": {"text/plain": ["DatasetRecords(Dataset(id=UUID('3a7abf40-a6b7-4cf6-ac09-d89a8b33ac67') inserted_at=datetime.datetime(2024, 4, 4, 5, 23, 44, 562080) updated_at=datetime.datetime(2024, 11, 15, 0, 35, 14, 753190) name='2-Data-Extractions' status='ready' guidelines=None allow_extra_metadata=True distribution=OverlapTaskDistributionModel(strategy='overlap', min_submitted=1) workspace_id=UUID('e9e4e699-a6f9-4482-b5dd-e45874bd87eb') last_activity_at=datetime.datetime(2024, 11, 21, 18, 7, 47, 105497)))"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset.records.log(updated_records)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["{'reference': 'mosqueira2015pilot',\n", " 'schema': {'fields': [{'name': 'observation_ref',\n", "    'type': 'any',\n", "    'extDtype': 'string'},\n", "   {'name': 'itncondition_ref', 'type': 'any', 'extDtype': 'string'},\n", "   {'name': 'N_people', 'type': 'integer'},\n", "   {'name': 'Age_lower', 'type': 'number'},\n", "   {'name': 'Age_upper', 'type': 'number'}],\n", "  'primaryKey': ['observation_ref', 'itncondition_ref'],\n", "  'pandas_version': '1.4.0'},\n", " 'data': [{'observation_ref': 'S01',\n", "   'itncondition_ref': 'N01',\n", "   'N_people': 3903,\n", "   'Age_lower': 0.5,\n", "   'Age_upper': 14.0},\n", "  {'observation_ref': 'S02',\n", "   'itncondition_ref': 'N01',\n", "   'N_people': 3903,\n", "   'Age_lower': 0.5,\n", "   'Age_upper': 14.0}],\n", " 'validation': {'schema_type': 'dataframe',\n", "  'version': '0.18.3',\n", "  'columns': {'N_people': {'title': None,\n", "    'description': 'Number of people in the study arm of the net in question',\n", "    'dtype': 'int64',\n", "    'nullable': True,\n", "    'checks': None,\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'Age_lower': {'title': None,\n", "    'description': 'Lower limit of age group in years. For children <1, enter age as a decimal.',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'Age_upper': {'title': None,\n", "    'description': 'Upper limit of age group in years. For children <1, enter age as a decimal.',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'N_pos': {'title': None,\n", "    'description': 'Number of people tested to be parasite positive',\n", "    'dtype': 'int64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'PR': {'title': None,\n", "    'description': 'Definition: (N_pos/N_people)*100',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'PR_rate_lower': {'title': None,\n", "    'description': 'Lower bound of parasite positivity rate',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'PR_rate_upper': {'title': None,\n", "    'description': 'Upper bound of parasite positivity rate',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'CM': {'title': None,\n", "    'description': 'Number of people with clinical malaria',\n", "    'dtype': 'int64',\n", "    'nullable': True,\n", "    'checks': None,\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'CM_rate': {'title': None,\n", "    'description': 'Definition: (CM/N_people)*100',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'CM_rate_lower': {'title': None,\n", "    'description': 'Lower bound of clinical malaria rate',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'CM_rate_upper': {'title': None,\n", "    'description': 'Upper bound of clinical malaria rate',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'Net_retention': {'title': None,\n", "    'description': 'Number of nets still owned divided by a number of nets previously distributed',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'N_nets': {'title': None,\n", "    'description': 'Number of nets found in household or community study arm',\n", "    'dtype': 'int64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'N_sleep_nets': {'title': None,\n", "    'description': 'Number of people that slept under a net the previous night',\n", "    'dtype': 'int64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'Perc_sleep_nets': {'title': None,\n", "    'description': 'Percent of people that slept under a net the previous night',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False}},\n", "  'checks': {'check_less_than': {'columns_a': ['Age_lower'],\n", "    'columns_b': ['Age_upper'],\n", "    'or_equal': True},\n", "   'check_greater_than': {'columns_a': 'N_people',\n", "    'columns_b': ['N_pos', 'CM', 'N_sleep_nets'],\n", "    'or_equal': True},\n", "   'check_between': {'columns_target': ['PR', 'CM_rate'],\n", "    'columns_lower': ['PR_rate_lower', 'CM_rate_lower'],\n", "    'columns_upper': ['PR_rate_upper', 'CM_rate_upper'],\n", "    'or_equal': True}},\n", "  'index': [{'title': 'Observation reference',\n", "    'description': None,\n", "    'dtype': 'str',\n", "    'nullable': <PERSON><PERSON><PERSON>,\n", "    'checks': {'str_startswith': 'S'},\n", "    'name': 'observation_ref',\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': False},\n", "   {'title': 'ITNCondition reference',\n", "    'description': None,\n", "    'dtype': 'str',\n", "    'nullable': <PERSON><PERSON><PERSON>,\n", "    'checks': {'str_startswith': 'N'},\n", "    'name': 'itncondition_ref',\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': False}],\n", "  'dtype': None,\n", "  'coerce': True,\n", "  'strict': True,\n", "  'name': 'ClinicalOutcome',\n", "  'ordered': <PERSON><PERSON><PERSON>,\n", "  'unique': None,\n", "  'report_duplicates': 'all',\n", "  'unique_column_names': <PERSON><PERSON><PERSON>,\n", "  'add_missing_columns': <PERSON><PERSON><PERSON>,\n", "  'title': None,\n", "  'description': '\\nEpidemiological and clinical outcomes on humans collected from a clinical trial or village trial, if reported in the study.\\nEach clinical outcome should have unique `observation_ref`, `itn_condition_ref`, `Group`, `Age_lower`, and `Age_upper` (if reported).\\n    '}}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["json.loads(record.fields[\"extraction\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test dataset"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Custom Field"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/Projects/extralit/argilla/src/argilla/client.py:354: UserWarning: Dataset with name 'interactive_chat' not found in workspace 'itn-recalibration'\n", "  warnings.warn(f\"Dataset with name {name!r} not found in workspace {workspace.name!r}\")\n"]}, {"ename": "AttributeError", "evalue": "'NoneType' object has no attribute 'delete'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 5\u001b[0m\n\u001b[1;32m      1\u001b[0m dataset \u001b[38;5;241m=\u001b[39m client\u001b[38;5;241m.\u001b[39mdatasets(\n\u001b[1;32m      2\u001b[0m     name\u001b[38;5;241m=\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124minteractive_chat\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m      3\u001b[0m     \u001b[38;5;66;03m# workspace=\"itn-recalibration\"\u001b[39;00m\n\u001b[1;32m      4\u001b[0m )\n\u001b[0;32m----> 5\u001b[0m \u001b[43mdataset\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdelete\u001b[49m()\n", "\u001b[0;31mAttributeError\u001b[0m: 'NoneType' object has no attribute 'delete'"]}], "source": ["dataset = client.datasets(\n", "    name=\"interactive_chat\",\n", "    # workspace=\"itn-recalibration\"\n", ")\n", "dataset.delete()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["Dataset(id=UUID('92b559e7-8eff-4d4c-85bf-817fd73570e4') inserted_at=datetime.datetime(2024, 12, 2, 21, 33, 33, 529345) updated_at=datetime.datetime(2024, 12, 2, 21, 33, 39, 111530) name='interactive_chat' status='ready' guidelines=None allow_extra_metadata=False distribution=OverlapTaskDistributionModel(strategy='overlap', min_submitted=1) workspace_id=UUID('e9e4e699-a6f9-4482-b5dd-e45874bd87eb') last_activity_at=datetime.datetime(2024, 12, 2, 21, 33, 39, 111530))"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["settings = ex.Settings(\n", "    fields=[\n", "        ex.<PERSON><PERSON>ield(name=\"chosen\", required=False),\n", "    ],\n", "    questions=[\n", "        ex.TableQuestion(\"extraction\", title=\"Correct the table\", required=True),\n", "    ],\n", ")\n", "\n", "dataset = ex.Dataset(\n", "    settings=settings,\n", "    name=\"interactive_chat\",\n", ")\n", "\n", "dataset.create()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["{'reference': 'mosqueira2015pilot',\n", " 'schema': {'fields': [{'name': 'observation_ref',\n", "    'type': 'any',\n", "    'extDtype': 'string'},\n", "   {'name': 'itncondition_ref', 'type': 'any', 'extDtype': 'string'},\n", "   {'name': 'N_people', 'type': 'integer'},\n", "   {'name': 'Age_lower', 'type': 'number'},\n", "   {'name': 'Age_upper', 'type': 'number'}],\n", "  'primaryKey': ['observation_ref', 'itncondition_ref'],\n", "  'pandas_version': '1.4.0'},\n", " 'data': [{'observation_ref': 'S01',\n", "   'itncondition_ref': 'N01',\n", "   'N_people': 3903,\n", "   'Age_lower': 0.5,\n", "   'Age_upper': 14.0},\n", "  {'observation_ref': 'S02',\n", "   'itncondition_ref': 'N01',\n", "   'N_people': 3903,\n", "   'Age_lower': 0.5,\n", "   'Age_upper': 14.0}],\n", " 'validation': {'schema_type': 'dataframe',\n", "  'version': '0.18.3',\n", "  'columns': {'N_people': {'title': None,\n", "    'description': 'Number of people in the study arm of the net in question',\n", "    'dtype': 'int64',\n", "    'nullable': True,\n", "    'checks': None,\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'Age_lower': {'title': None,\n", "    'description': 'Lower limit of age group in years. For children <1, enter age as a decimal.',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'Age_upper': {'title': None,\n", "    'description': 'Upper limit of age group in years. For children <1, enter age as a decimal.',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'N_pos': {'title': None,\n", "    'description': 'Number of people tested to be parasite positive',\n", "    'dtype': 'int64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'PR': {'title': None,\n", "    'description': 'Definition: (N_pos/N_people)*100',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'PR_rate_lower': {'title': None,\n", "    'description': 'Lower bound of parasite positivity rate',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'PR_rate_upper': {'title': None,\n", "    'description': 'Upper bound of parasite positivity rate',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'CM': {'title': None,\n", "    'description': 'Number of people with clinical malaria',\n", "    'dtype': 'int64',\n", "    'nullable': True,\n", "    'checks': None,\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'CM_rate': {'title': None,\n", "    'description': 'Definition: (CM/N_people)*100',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'CM_rate_lower': {'title': None,\n", "    'description': 'Lower bound of clinical malaria rate',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'CM_rate_upper': {'title': None,\n", "    'description': 'Upper bound of clinical malaria rate',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'Net_retention': {'title': None,\n", "    'description': 'Number of nets still owned divided by a number of nets previously distributed',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'N_nets': {'title': None,\n", "    'description': 'Number of nets found in household or community study arm',\n", "    'dtype': 'int64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'N_sleep_nets': {'title': None,\n", "    'description': 'Number of people that slept under a net the previous night',\n", "    'dtype': 'int64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False},\n", "   'Perc_sleep_nets': {'title': None,\n", "    'description': 'Percent of people that slept under a net the previous night',\n", "    'dtype': 'float64',\n", "    'nullable': True,\n", "    'checks': {'greater_than_or_equal_to': 0, 'less_than_or_equal_to': 100},\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': <PERSON><PERSON><PERSON>,\n", "    'required': True,\n", "    'regex': False}},\n", "  'checks': {'check_less_than': {'columns_a': ['Age_lower'],\n", "    'columns_b': ['Age_upper'],\n", "    'or_equal': True},\n", "   'check_greater_than': {'columns_a': 'N_people',\n", "    'columns_b': ['N_pos', 'CM', 'N_sleep_nets'],\n", "    'or_equal': True},\n", "   'check_between': {'columns_target': ['PR', 'CM_rate'],\n", "    'columns_lower': ['PR_rate_lower', 'CM_rate_lower'],\n", "    'columns_upper': ['PR_rate_upper', 'CM_rate_upper'],\n", "    'or_equal': True}},\n", "  'index': [{'title': 'Observation reference',\n", "    'description': None,\n", "    'dtype': 'str',\n", "    'nullable': <PERSON><PERSON><PERSON>,\n", "    'checks': {'str_startswith': 'S'},\n", "    'name': 'observation_ref',\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': False},\n", "   {'title': 'ITNCondition reference',\n", "    'description': None,\n", "    'dtype': 'str',\n", "    'nullable': <PERSON><PERSON><PERSON>,\n", "    'checks': {'str_startswith': 'N'},\n", "    'name': 'itncondition_ref',\n", "    'unique': <PERSON><PERSON><PERSON>,\n", "    'coerce': False}],\n", "  'dtype': None,\n", "  'coerce': True,\n", "  'strict': True,\n", "  'name': 'ClinicalOutcome',\n", "  'ordered': <PERSON><PERSON><PERSON>,\n", "  'unique': None,\n", "  'report_duplicates': 'all',\n", "  'unique_column_names': <PERSON><PERSON><PERSON>,\n", "  'add_missing_columns': <PERSON><PERSON><PERSON>,\n", "  'title': None,\n", "  'description': '\\nEpidemiological and clinical outcomes on humans collected from a clinical trial or village trial, if reported in the study.\\nEach clinical outcome should have unique `observation_ref`, `itn_condition_ref`, `Group`, `Age_lower`, and `Age_upper` (if reported).\\n    '}}"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["sample_table = record.fields[\"extraction\"]\n", "sample_table"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\">DatasetRecords: The provided batch size <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">256</span> was normalized. Using value <span style=\"color: #008080; text-decoration-color: #008080; font-weight: bold\">4</span>.\n", "</pre>\n"], "text/plain": ["DatasetRecords: The provided batch size \u001b[1;36m256\u001b[0m was normalized. Using value \u001b[1;36m4\u001b[0m.\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Sending records...: 100%|███████| 1/1 [00:02<00:00,  2.54s/batch]\n"]}, {"data": {"text/plain": ["DatasetRecords(Dataset(id=UUID('a64a827c-f962-417a-a771-ce53f61c0756') inserted_at=datetime.datetime(2024, 11, 29, 23, 9, 55, 104623) updated_at=datetime.datetime(2024, 11, 29, 23, 9, 58, 696913) name='interactive_chat' status='ready' guidelines=None allow_extra_metadata=False distribution=OverlapTaskDistributionModel(strategy='overlap', min_submitted=1) workspace_id=UUID('e9e4e699-a6f9-4482-b5dd-e45874bd87eb') last_activity_at=datetime.datetime(2024, 11, 29, 23, 9, 58, 696913)))"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset.records.log([{\"chosen\": sample_table} for r in updated_records])"]}, {"cell_type": "code", "execution_count": 9, "metadata": {"scrolled": true}, "outputs": [], "source": ["# ds = load_dataset(\"argilla/Capybara-Preferences\", split=\"train[:100]\")\n", "# ds = ds.map(lambda x: {\"messages\": x[\"chosen\"]})\n", "# dataset.records.log(ds)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 4}