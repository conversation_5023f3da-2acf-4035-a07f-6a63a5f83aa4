/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'info': {
    width: 41,
    height: 40,
    viewBox: '0 0 41 40',
    data: '<path pid="0" d="M19 18.47a1.5 1.5 0 113 0v9a1.5 1.5 0 01-3 0v-9zM20.5 11.077a1.5 1.5 0 100 3 1.5 1.5 0 000-3z" _fill="#000"/><path pid="1" fill-rule="evenodd" clip-rule="evenodd" d="M20.5 5c-8.284 0-15 6.716-15 15 0 8.284 6.716 15 15 15 8.284 0 15-6.716 15-15 0-8.284-6.716-15-15-15zm-12 15c0 6.627 5.373 12 12 12s12-5.373 12-12-5.373-12-12-12-12 5.373-12 12z" _fill="#000"/>'
  }
})