/*!
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Mixins
// -----------
// Truncate text
@mixin truncate($width: 100%) {
  // display: inline-block;
  // vertical-align: middle;
  width: $width;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

//  http://zerosixthree.se/8-sass-mixins-you-must-have-in-your-toolbox/
//
//  Set a rem font size with pixel fallback
//
//  --- USAGE:
//
//  p {
//    @include font-size(14px)
//  }
@mixin font-size($size) {
  $units: "px" !default;
  $units: unit($size);
  @if ($units== "%") {
    font-size: $size;
  } @else {
    @if unitless($size) {
      @warn "Assuming #{$size} to be in pixels";
      $size: 1px * $size;
    }
    font-size: $size;
    font-size: calculateRem($size);
  }
}

@mixin line-height($size) {
  $units: "px" !default;
  $units: unit($size);
  @if ($units== "%") {
    line-height: $size;
  } @else {
    @if unitless($size) {
      @warn "Assuming #{$size} to be in pixels";
      $size: 1px * $size;
    }
    line-height: $size;
    line-height: calculateRem($size);
  }
}

//  Retina Images
//
//  --- USAGE:
//
//  div.logo {
//    background: url("logo.png") no-repeat;
//    @include image-2x("logo2x.png", 100px, 25px);
//  }
@mixin image-2x($image, $width, $height) {
  @media (min--moz-device-pixel-ratio: 1.3),
    (-o-min-device-pixel-ratio: 2.6/2),
    (-webkit-min-device-pixel-ratio: 1.3),
    (min-device-pixel-ratio: 1.3),
    (min-resolution: 1.3dppx) {
    /* on retina, use image that's scaled by 2 */
    background-image: url($image);
    background-size: $width $height;
  }
}

//  Aspect Ratio for VIDEOS
//
//  --- USAGE:
//
//  .video {
//    @include aspect-ratio( 16, 9 );
//  }
@mixin aspect-ratio($width, $height) {
  position: relative;
  overflow: hidden;
  &::before {
    display: block;
    content: "";
    width: 100%;
    padding-top: ($height / $width) * 100%;
  } // > .#{element} {
  > iframe,
  > video,
  > img {
    display: block;
    width: auto;
    height: 110%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } // > video {
  //     width: 100%;
  //     height: auto;
  // }
}

// proportions
@mixin proportions($width, $height) {
  position: relative;
  &::before {
    display: block;
    content: "";
    width: 100%;
    padding-top: ($height / $width) * 100%;
  }
}

// placeholder
@mixin input-placeholder {
  &.placeholder {
    @content;
  }
  &:-moz-placeholder {
    @content;
  }
  &::-moz-placeholder {
    @content;
  }
  &:-ms-input-placeholder {
    @content;
  }
  &::-webkit-input-placeholder {
    @content;
  }
}

// Adds animation
@mixin animation(
  $name,
  $timing-function,
  $duration,
  $delay: 0ms,
  $iteration-count: 1,
  $direction: normal,
  $fill-mode: forwards
) {
  animation: $name $timing-function $duration $delay $iteration-count $direction
    $fill-mode;
}

// Adds transition
@mixin transition($property, $time: 0.3s, $cb: $cb-fast) {
  transition: $property $time $cb;
}

// Vertical gradient, from top to bottom
//
// Creates two color stops, start and end, by specifying a color and position for each color stop.
// Color stops are not available in IE9 and below.
@mixin gradient-vertical(
  $start-color: #555,
  $end-color: #333,
  $start-percent: 0%,
  $end-percent: 100%
) {
  background-image: -webkit-linear-gradient(
    top,
    $start-color $start-percent,
    $end-color $end-percent
  ); // Safari 5.1-6, Chrome 10+
  background-image: -o-linear-gradient(
    top,
    $start-color $start-percent,
    $end-color $end-percent
  ); // Opera 12
  background-image: linear-gradient(
    to bottom,
    $start-color $start-percent,
    $end-color $end-percent
  ); // Standard, IE10, Firefox 16+, Opera 12.10+, Safari 7+, Chrome 26+
  background-repeat: repeat-x;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{ie-hex-str($start-color)}', endColorstr='#{ie-hex-str($end-color)}', GradientType=0); // IE9 and down
}

// Adds styles to any text
@mixin style($fSize, $color: palette(black), $font: $ff) {
  // $units: "px" !default;
  $units: unit($fSize); // @debug $units;
  @if $units== "rem" {
    font-size: $fSize;
  } @else {
    @if unitless($fSize) {
      @warn "Assuming #{$size} to be in pixels";
      $fSize: 1px * $fSize;
    }
    font-size: calculateRem($fSize);
  }
  font-family: $font;
  color: $color;
}

// triangle
/// Triangle helper mixin
/// @param {Direction} $direction - Triangle direction, either `top`, `right`, `bottom` or `left`
/// @param {Color} $color [currentcolor] - Triangle color
/// @param {Length} $size [1em] - Triangle size
@mixin triangle($direction, $sizeH, $sizeV, $color) {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-style: solid;
  @if $direction==top {
    border-width: 0 $sizeV $sizeH $sizeV;
    border-color: transparent transparent $color transparent;
  }
  @if $direction==bottom {
    border-width: $sizeV $sizeH 0 $sizeV;
    border-color: $color transparent transparent transparent;
  }
  @if $direction==left {
    border-width: $sizeV $sizeH $sizeV 0;
    border-color: transparent $color transparent transparent;
  }
  @if $direction==right {
    border-width: $sizeV 0 $sizeV $sizeH;
    border-color: transparent transparent transparent $color;
  }
  @if $direction==topright {
    border-width: 0 $sizeH $sizeV 0;
    border-color: transparent $color transparent transparent;
  }
  @if $direction==bottomright {
    border-width: 0 0 $sizeH $sizeV;
    border-color: transparent transparent $color transparent;
  }
  @if $direction==bottomleft {
    border-width: $sizeH 0 0 $sizeV;
    border-color: transparent transparent transparent $color;
  }
  @if $direction==topleft {
    border-width: $sizeH $sizeV 0 0;
    border-color: $color transparent transparent transparent;
  }
}

// vertical align
@mixin ghostVerticalAlign() {
  &:before {
    content: "";
    display: inline-block;
    vertical-align: middle;
    height: 100%;
    width: 0.1px;
  }
}

// center
@mixin absoluteCenter() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin resetButtonStyles() {
  outline: none;
  border: none;
  background: none;
  margin: auto;
}

@mixin overflow-scrollbar {
  &::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 10px;
  }

  &:hover {
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 2px grey;
      border-radius: 10px;
    }

    &::-webkit-scrollbar-thumb {
      background: #888; // Change the thumb color on hover
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }
}