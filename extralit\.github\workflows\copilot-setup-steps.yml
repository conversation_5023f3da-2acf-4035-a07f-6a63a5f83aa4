name: "Copilot Setup Steps"

# Automatically run the setup steps when they are changed to allow for easy validation, and
# allow manual testing through the repository's "Actions" tab
on:
  workflow_dispatch:
  push:
    paths:
      - .github/workflows/copilot-setup-steps.yml
  pull_request:
    paths:
      - .github/workflows/copilot-setup-steps.yml

jobs:
  copilot-setup-steps:
    runs-on: ubuntu-latest

    services:
      elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch:8.17.0
        ports:
          - 9200:9200
        env:
          discovery.type: single-node
          xpack.security.enabled: false

      redis:
        image: redis
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

      minio:
        image: lazybit/minio
        volumes:
          - /data:/data
        env:
          MINIO_ACCESS_KEY: minioadmin
          MINIO_SECRET_KEY: minioadmin
        options: --name=minio --health-cmd "curl http://localhost:9000/minio/health/live"
        ports:
          - 9000:9000

    env:
      HF_HUB_DISABLE_TELEMETRY: 1

    permissions:
      contents: read # for actions/checkout

    # Define steps that will run before the Copilot agent starts
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          pyproject-file: "extralit-server/pyproject.toml"
          python-version: "3.11"
          enable-cache: true
          cache-local-path: ~/.cache/uv
          cache-dependency-glob: "extralit-server/pdm.lock"

      - name: Setup Python env and Install extralit-server editable package
        env:
          PDM_IGNORE_ACTIVE_VENV: 1
        run: |
          uv tool install pdm
          pdm config use_uv true
          pdm config python.install_root "$(uv python dir)"

          cd extralit-server/
          pdm install -G test

      - name: Install extralit editable package with dependencies
        working-directory: extralit
        run: |
          uv pip install -e ".[dev]"

      - name: Install Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: "npm"
          cache-dependency-path: extralit-frontend/package-lock.json

      - name: Setup extralit-frontend dependencies
        working-directory: extralit-frontend
        env:
          API_BASE_URL: http://localhost:3000
        run: |
          npm install

      - name: Pre-run database migrations
        working-directory: extralit-server
        env:
          HOME: /home/<USER>
        run: |
          mkdir -p /home/<USER>/.extralit
          pdm run alembic -c src/extralit_server/alembic.ini upgrade head

    #   - name: Verify critical dependencies
    #     run: |
    #       cd extralit-server && pdm run python -c "import pandera; print('✓ Pandera available')" || echo "⚠ Pandera not available"
