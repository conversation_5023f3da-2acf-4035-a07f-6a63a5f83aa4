# Copyright 2024-present, Extralit Labs, Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import httpx
from httpx import HTTPStatusError

__all__ = ["raise_for_status"]


def raise_for_status(response: httpx.Response) -> None:
    """Raise an exception if the response status code is not a 2xx code."""
    try:
        response.raise_for_status()
    except httpx.HTTPStatusError as e:
        if e.response.is_client_error:
            message = (f"{e.response.json()} {e!r}",)
        else:
            message = f"{e!r}. Response: {e.response.content!r}"

        raise HTTPStatusError(message=message, request=e.request, response=e.response) from e
