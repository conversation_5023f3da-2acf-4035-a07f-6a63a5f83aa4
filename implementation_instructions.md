# AI Implementation Instructions: PyMuPDF RQ Integration

## Context
You are implementing the PyMuPDF extraction integration with the Extralit RQ workflow. Follow the plan.md checklist exactly - no over-engineering, minimal changes to production Extralit code.

## Environment Setup
- Work in `d:\Extralit-gsoc` directory
- Use venv in `d:\Extralit-gsoc\extralit-hf-space\venv` when working on extralit-hf-space
- Use venv in `d:\Extralit-gsoc\extralit\venv` when working on extralit
- Use appropriate Python environment for extralit-server changes
- Make git commits after each logical change with descriptive messages
- Update plan.md checkboxes ✅ as you complete each task

## Implementation Order (Follow Exactly)

### Phase 1: Core Infrastructure Setup

#### Task 1.1: Add PDF_QUEUE to extralit-server
```bash
# Navigate to extralit-server
cd d:\Extralit-gsoc\extralit\extralit-server
```

**File**: `src/extralit_server/jobs/queues.py`
**Change**: Add this line after existing queues:
```python
PDF_QUEUE = Queue("pdf_queue", connection=REDIS_CONNECTION)
```

**Git**: `git add . && git commit -m "feat: add PDF_QUEUE for PyMuPDF extraction jobs"`

**Plan Update**: Mark task 1.1 as ✅ in plan.md

#### Task 1.2: Remove FastAPI endpoints from extralit-hf-space
```bash
# Navigate to extralit-hf-space and activate venv
cd d:\Extralit-gsoc\extralit-hf-space
venv\Scripts\activate
```

**File**: `src/app.py`
**Change**: Replace entire file content with minimal health check only:
```python
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Extralit PDF Extraction Service", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,
    allow_methods=["GET"],
    allow_headers=["*"],
)

@app.get("/healthz")
async def healthz():
    return {"status": "ok", "service": "pdf-extraction-rq"}

@app.get("/")
async def root():
    return {"message": "RQ-based PDF extraction service", "status": "active"}
```

**Git**: `git add . && git commit -m "refactor: remove FastAPI endpoints, keep RQ-only processing"`

**Plan Update**: Mark task 1.2 as ✅ in plan.md

#### Task 1.3: Create RQ-only PDF extraction job
**File**: `src/jobs/pdf_extraction_jobs.py` (create new file)
**Content**:
```python
"""
RQ job for PDF extraction using PyMuPDF with S3 integration.
"""

import logging
import time
from typing import Any, Dict, Optional
from uuid import UUID
from datetime import datetime, timezone

from rq import get_current_job
from rq.decorators import job

# Import extralit_server modules for S3 and database operations
try:
    from extralit_server.contexts.files import get_minio_client, download_file_content
    from extralit_server.api.schemas.v1.document.metadata import DocumentProcessingMetadata
    from extralit_server.models.database import Document
    from extralit_server.database import SyncSessionLocal
except ImportError as e:
    logging.warning(f"extralit_server imports not available: {e}")

# Import local extraction logic
from ..extract import extract_markdown_with_hierarchy
from ..redis_connection import get_redis_connection

_LOGGER = logging.getLogger(__name__)


@job(queue='pdf_queue', connection=get_redis_connection(), timeout=900, result_ttl=3600)
def extract_pdf_from_s3_job(
    document_id: UUID,
    s3_url: str,
    filename: str,
    analysis_metadata: Dict[str, Any],
    workspace_name: str
) -> Dict[str, Any]:
    """
    Extract PDF text using PyMuPDF, downloading from S3.

    Args:
        document_id: UUID of document to process
        s3_url: S3 URL of the PDF file
        filename: Original filename
        analysis_metadata: Results from analysis_and_preprocess_job
        workspace_name: Workspace name for S3 operations

    Returns:
        Dictionary with extraction results
    """
    current_job = get_current_job()
    current_job.meta.update({
        'document_id': str(document_id),
        'filename': filename,
        'workspace_name': workspace_name,
        'workflow_step': 'pymupdf_extraction',
        'started_at': datetime.now(timezone.utc).isoformat()
    })
    current_job.save_meta()

    try:
        # Step 1: Download PDF from S3
        client = get_minio_client()
        if client is None:
            raise Exception("Failed to get storage client")

        pdf_data = download_file_content(client, s3_url)
        _LOGGER.info(f"Downloaded PDF from S3: {s3_url} ({len(pdf_data)} bytes)")

        # Step 2: Extract markdown using PyMuPDF
        extraction_start = time.time()
        markdown, extraction_metadata = extract_markdown_with_hierarchy(pdf_data, filename)
        extraction_time = time.time() - extraction_start

        # Step 3: Prepare results
        result = {
            'document_id': str(document_id),
            'markdown': markdown,
            'extraction_metadata': extraction_metadata,
            'processing_time': extraction_time,
            'success': True
        }

        # Step 4: Update document metadata in database
        with SyncSessionLocal() as db:
            document = db.get(Document, document_id)
            if document and document.metadata_:
                metadata = DocumentProcessingMetadata(**document.metadata_)

                # Add text extraction metadata
                from extralit_server.api.schemas.v1.document.metadata import TextExtractionMetadata
                metadata.text_extraction_metadata = TextExtractionMetadata(
                    extracted_text_length=len(markdown),
                    extraction_method="pymupdf4llm",
                    text_extraction_completed_at=datetime.now(timezone.utc)
                )

                document.metadata_ = metadata.model_dump()
                db.commit()
                _LOGGER.info(f"Updated document {document_id} metadata with extraction results")

        current_job.meta.update({
            'completed_at': datetime.now(timezone.utc).isoformat(),
            'success': True,
            'text_length': len(markdown)
        })
        current_job.save_meta()

        return result

    except Exception as e:
        _LOGGER.error(f"Error in PyMuPDF extraction for document {document_id}: {e}")
        current_job.meta.update({
            'completed_at': datetime.now(timezone.utc).isoformat(),
            'success': False,
            'error': str(e)
        })
        current_job.save_meta()
        raise
```

**Git**: `git add . && git commit -m "feat: add S3-based PyMuPDF extraction RQ job"`

**Plan Update**: Mark task 1.3 as ✅ in plan.md

#### Task 1.4: Update worker configuration
**File**: `src/worker.py`
**Change**: Replace entire content:
```python
import os
import sys
import redis
from rq import Worker, Queue

# Import PDF extraction jobs to register them
from .jobs.pdf_extraction_jobs import extract_pdf_from_s3_job  # noqa: F401

REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
# Primary queue for direct RQ communication with extralit-server
QUEUES = os.getenv("RQ_QUEUES", "pdf_queue,high_priority,low_priority").split(",")

def main():
    conn = redis.from_url(REDIS_URL)
    queues = [Queue(name.strip(), connection=conn) for name in QUEUES if name.strip()]

    # Windows compatibility: use SimpleWorker which doesn't fork
    if sys.platform.startswith('win'):
        from rq import SimpleWorker
        w = SimpleWorker(queues, connection=conn)
        print("🪟 Starting SimpleWorker for Windows compatibility...")
        print(f"📋 Listening on queues: {[q.name for q in queues]}")
    else:
        w = Worker(queues, connection=conn)
        print("🐧 Starting standard Worker...")
        print(f"📋 Listening on queues: {[q.name for q in queues]}")

    w.work(logging_level="INFO")

if __name__ == "__main__":
    main()
```

**Git**: `git add . && git commit -m "refactor: update worker to use pdf_queue and import extraction jobs"`

**Plan Update**: Mark task 1.4 as ✅ in plan.md

### Phase 2: Integration with Extralit Workflow

#### Task 2.1: Modify start_pdf_workflow
```bash
# Navigate back to extralit-server
cd d:\Extralit-gsoc\extralit\extralit-server
```

**File**: `src/extralit_server/workflows/pdf.py`
**Change**: Add import at top and modify workflow function:

Add import:
```python
from extralit_server.jobs.queues import DEFAULT_QUEUE, PDF_QUEUE
```

Modify `start_pdf_workflow` function - add after analysis_job creation:
```python
        # Step 3: Enqueue PyMuPDF extraction job (depends on analysis)
        pymupdf_job = PDF_QUEUE.enqueue(
            'extract_pdf_from_s3_job',
            document_id, s3_url, s3_url.split("/")[-1], {}, workspace_name,
            depends_on=[analysis_job],
            job_timeout=900,
            job_id=f"pymupdf_{document_id}"
        )
```

Update job_ids dict:
```python
        job_ids = {
            "analysis_and_preprocess": analysis_job.id,
            "pymupdf_extraction": pymupdf_job.id,
            "workflow_id": str(workflow.id),
        }
```

**Git**: `git add . && git commit -m "feat: integrate PyMuPDF extraction job into PDF workflow"`

**Plan Update**: Mark task 2.1 as ✅ in plan.md

#### Task 2.2: Update job imports
**File**: `src/jobs/__init__.py` (in extralit-hf-space)
```bash
cd d:\Extralit-gsoc\extralit-hf-space
venv\Scripts\activate
```

**Content**:
```python
"""
Job modules for PDF extraction service.
"""

from .pdf_extraction_jobs import extract_pdf_from_s3_job

__all__ = ['extract_pdf_from_s3_job']
```

**Git**: `git add . && git commit -m "feat: add job imports for RQ worker registration"`

**Plan Update**: Mark task 2.2 as ✅ in plan.md

### Phase 3: Testing and Validation

#### Task 3.1: Test worker startup
```bash
# In extralit-hf-space with venv activated
cd d:\Extralit-gsoc\extralit-hf-space
venv\Scripts\activate
python -m src.worker
```

**Verify**: Worker starts without errors and shows "Listening on queues: ['pdf_queue', ...]"

**Git**: `git add . && git commit -m "test: verify worker startup with PDF extraction jobs"`

**Plan Update**: Mark task 3.1 as ✅ in plan.md

#### Task 3.2: Create simple integration test
**File**: `test_integration.py` (in extralit-hf-space root)
**Content**:
```python
"""
Simple integration test for PDF extraction job.
"""

import os
import sys
sys.path.insert(0, 'src')

from redis_connection import get_queue, PDF_QUEUE
from jobs.pdf_extraction_jobs import extract_pdf_from_s3_job

def test_job_registration():
    """Test that job can be enqueued."""
    try:
        queue = get_queue(PDF_QUEUE)
        print(f"✅ Successfully got queue: {queue.name}")
        print(f"✅ Queue length: {len(queue)}")
        return True
    except Exception as e:
        print(f"❌ Error getting queue: {e}")
        return False

if __name__ == "__main__":
    success = test_job_registration()
    if success:
        print("🎉 Integration test passed")
    else:
        print("💥 Integration test failed")
```

**Run**: `python test_integration.py`

**Git**: `git add . && git commit -m "test: add integration test for job registration"`

**Plan Update**: Mark task 3.2 as ✅ in plan.md

## Completion Steps

1. **Update plan.md**: Mark all completed tasks as ✅
2. **Final commit**: `git add plan.md && git commit -m "docs: update implementation plan with completed tasks"`
3. **Test end-to-end**: Verify worker can process jobs from pdf_queue

## Rules to Follow

1. **No over-engineering**: Stick to the exact plan, don't add extra features
2. **Minimal changes**: Only touch files mentioned in the plan
3. **Git commit each logical change**: Small, focused commits with clear messages
4. **Update plan.md**: Check off ✅ each completed task
5. **Use correct environments**: venv for extralit-hf-space, appropriate env for extralit-server
6. **Error handling**: If something fails, fix the minimum needed and continue

## Success Criteria

- [ ] PDF_QUEUE added to extralit-server
- [ ] FastAPI endpoints removed from extralit-hf-space
- [ ] PyMuPDF extraction job created and registered
- [ ] Worker starts and listens to pdf_queue
- [ ] start_pdf_workflow calls PyMuPDF job
- [ ] All changes committed with descriptive messages
- [ ] plan.md updated with ✅ checkmarks

**Start with Task 1.1 and work through each task in order. Do not skip or reorder tasks.**
