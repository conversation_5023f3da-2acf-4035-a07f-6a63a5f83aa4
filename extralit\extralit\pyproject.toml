[project]
name = "extralit"
description = "Open-source tool for accurate & fast scientific literature data extraction with LLM and human-in-the-loop."
authors = [
    { name = "Extralit Labs", email = "<EMAIL>" },
]
maintainers = [
    { name = "Extralit Labs", email = "<EMAIL>" },
]
requires-python = ">=3.9.2, <3.14"
readme = "README.md"
license = { text = "Apache 2.0" }

dynamic = ["version"]

dependencies = [
    "httpx>=0.26.0",
    "pydantic>=2.6.0, <3.0.0",
    "huggingface_hub>=0.22.0",
    "tqdm>=4.60.0",
    "rich>=10.0.0",
    "datasets>=3.0.0",
    "pillow>=9.5.0",
    "standardwebhooks>=1.0.0",
    "typer>=0.9.0",
    "lazy-loader>=0.4",

    # for environment variables
    "python-dotenv~=1.1.0",

    # for extralit
    "minio ~= 7.2.15",
    "html5lib ~= 1.1",
    "fastapi < 1.0.0",
    "pypandoc ~= 1.13",
    "beautifulsoup4 ~= 4.12.2",
    "pandas ~= 2.2.2",
    "pandera[io] >= 0.21.0, < 0.27.0",
    "numpy >= 1.26.4",
    "spacy ~= 3.7.2; python_version < '3.13'",
    "spacy >= 3.8.0; python_version >= '3.13' and python_version < '3.13.3'",
    # Install spaCy with wheels only for Python 3.13.3+
    "spacy-wheel >= 3.8.0; python_version >= '3.13.3'",
    "pyarrow >= 14.0.0, != 14.0.2; python_version < '3.13'",
    "pyarrow >= 15.0.0; python_version >= '3.13'",
    "natsort ~= 8.4.0",
    "rapidfuzz ~= 3.8.1",
    "dill ~= 0.3.8",
    "json-repair ~= 0.19.2",
    "fastparquet >= 2023.10.0; python_version < '3.13'",
    "fastparquet >= 2024.4.0; python_version >= '3.13'",
    "tiktoken ~= 0.9.0",
    "bibtexparser>=1.4.3",

    # for llama-index
    "llama-index ~= 0.10.68",
    "llama-index-core ~= 0.10.68",
    "llama-index-callbacks-langfuse ~= 0.1.6",
    "llama-index-llms-openai ~= 0.1.31",
    "llama-index-embeddings-openai ~=0.1.11",
    "llama-index-multi-modal-llms-openai",

    # for weaviate vector db
    "weaviate-client >= 4",
    "llama-index-vector-stores-weaviate ~= 1.0.0",
]
nlp = [
    "textdescriptives",
    "setfit ~= 0.7.0",
]
ocr = [
    "nougat-ocr[api]",
    "timm == 0.5.4; python_version < '3.13'",
    "timm == 0.9.5; python_version >= '3.13'",
    "transformers ~= 4.25.1"
]
pdf = [
    "unstructured[pdf] ~= 0.12.3",
    "llmsherpa ~= 0.1.3",
    "python-doctr ~= 0.8.1",
    "deepdoctection",
    "pypdf ~= 4.3.1",
    "pypdfium2",
    "pymupdf==1.26.0",
    "pdf2image ~= 1.16.0",
]

[build-system]
requires = ["pdm-backend>=2.0.0", "setuptools>=68.0.0"]
build-backend = "pdm.backend"

[tool.ruff]
line-length = 120

[tool.ruff.lint]
select = [
    "F",    # Pyflakes (undefined names, logical issues)
    "E7",   # Syntax/indentation errors
    "E9",   # Runtime-like errors
    "E4",   # Import-related errors (E401, E402, etc.)
    "B012", # return in finally (can suppress real exceptions)
    "B",    # flake8-bugbear (broader than just B012)
    "C4",   # flake8-comprehensions
    "I",    # isort (import sorting)
    "UP",   # pyupgrade (modern Python syntax)
    "ASYNC", # flake8-async - async/await specific rules
    "PLE",   # pylint errors (includes await-outside-async)
    "FAST",  # FastAPI-specific rules (valuable for your backend)
    "RUF",   # ruff-specific rules
]
ignore = [
    "E402",
    "B904",
    "UP007",
    "UP045",
    "B027",
    "B024",
    "B017",
    "RUF012",
    "B008",
    "FAST002",
    "B023"
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401", "F403", "I001"]
"tests/**/*.py" = ["ASYNC", "F821"]
"src/extralit/cli/**/*.py" = ["UP007", "UP045"]

[tool.black]
line-length = 120

[tool.pdm]
distribution = true
use-venv = true

[tool.pdm.version]
source = "file"
path = "src/extralit/_version.py"

[tool.pdm.dev-dependencies]
dev = [
    "ipython>=8.12.3",
    "pytest>=7.4.4",
    "pytest-cov>=4.1.0",
    "pytest-asyncio~=0.21.1",
    "flake8>=5.0.4",
    "ruff>=0.1.12",
    "pytest-mock>=3.12.0",
    "pytest-httpx ~=0.29.0",
    "black>=23.12.1",
    "build>=1.0.3",
    "pre-commit>=3.5.0",
    "mkdocs-material >= 9.5.17",
    "mkdocstrings[python] >= 0.24.0",
    "mkdocs-literate-nav >= 0.6.1",
    "mkdocs-section-index >= 0.3.8",
    "mkdocs-gen-files >= 0.5.0",
    "mkdocs-open-in-new-tab >= 1.0.3",
    "material-plausible-plugin>=0.2.0",
    "mike >= 2.0.0",
    "Pillow >= 9.5.0",
    "CairoSVG >= 2.7.1",
    "mknotebooks >= 0.8.0",
    "pytest-retry>=1.5",
]

[tool.pdm.scripts]
test = { cmd = "pytest tests --disable-warnings", env_file = ".env.test" }
test-cov = { cmd = "pytest tests --disable-warnings --cov=extralit --cov-report=term --cov-report=xml", env_file = ".env.test" }
lint = "ruff check"
format = "black ."
all = { composite = ["format", "lint", "test"] }

[project.scripts]
extralit = "extralit.cli.app:app"

[tool.pytest.ini_options]
testpaths = ["tests"]
env = ["HF_HUB_DISABLE_TELEMETRY=1"]
