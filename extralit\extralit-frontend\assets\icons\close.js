/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'close': {
    width: 41,
    height: 40,
    viewBox: '0 0 41 40',
    data: '<path pid="0" d="M8.922 5.587a2.005 2.005 0 10-2.835 2.835L17.665 20 6.087 31.577a2.005 2.005 0 102.836 2.836L20.5 22.835l11.577 11.578a2.005 2.005 0 002.836-2.836L23.335 20 34.913 8.422a2.005 2.005 0 00-2.835-2.835L20.5 17.165 8.922 5.587z" _fill="#000"/>'
  }
})