<template>
  <div class="input-container" :class="[classes]">
    <slot />

    <span v-if="enableCounter" class="re-count">{{ inputLength }} / {{ counterLength }}</span>
  </div>
</template>

<script>
export default {
  props: {
    reInline: Boolean,
    reClearable: Boolean,
  },
  data() {
    return {
      value: "",
      input: false,
      inputInstance: null,
      enableCounter: false,
      hasSelect: false,
      hasPlaceholder: false,
      hasFile: false,
      isDisabled: false,
      isRequired: false,
      isFocused: false,
      counterLength: 0,
      inputLength: 0,
    };
  },
  computed: {
    hasValue() {
      if (Array.isArray(this.value)) {
        return this.value.length > 0;
      }

      return Boolean(this.value);
    },
    classes() {
      return {
        "re-input-inline": this.reInline,
        "re-clearable": this.reClearable,
        "re-has-select": this.hasSelect,
        "re-has-file": this.hasFile,
        "re-has-value": this.hasValue,
        "re-input-placeholder": this.hasPlaceholder,
        "re-input-disabled": this.isDisabled,
        "re-input-required": this.isRequired,
        "re-input-focused": this.isFocused,
      };
    },
  },
  mounted() {
    this.input = this.$el.querySelectorAll("input, textarea, select, .re-file")[0];

    if (!this.input) {
      this.$destroy();

      throw new Error("Missing input/select/textarea inside re-input-container");
    }
  },
  methods: {
    isInput() {
      return this.input && this.input.tagName.toLowerCase() === "input";
    },
    clearInput() {
      this.inputInstance.$el.value = "";
      this.inputInstance.$emit("input", "");
      this.setValue("");
    },
    setValue(value) {
      this.value = value;
    },
  },
};
</script>

<style lang="scss" scoped>
input:-webkit-autofill {
  box-shadow: 0 0 0px 1000px var(--color-white) inset;
}

.input-container {
  input,
  textarea {
    width: 100%;
    height: $input-size;
    padding: 0;
    display: block;
    flex: 1;
    border: none !important;
    background: none;
    transition: $swift-ease-out;
    transition-property: font-size;
    color: var(--fg-primary);
    line-height: normal;
    @include input-placeholder {
      color: var(--fg-secondary);
      font-weight: 400;
    }
    &:focus {
      outline: none;
    }
  }
}
</style>
