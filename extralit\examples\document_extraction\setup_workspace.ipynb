{"cells": [{"cell_type": "markdown", "id": "f9d7358b", "metadata": {}, "source": ["# Setting Up Workspaces in Extralit\n", "\n", "In this tutorial, we will learn how to set up and manage workspaces in Extralit using the default credentials on a fresh installation. It will walk you through the following steps:\n", "\n", "1. Connecting to Extralit with default credentials 🔑\n", "2. Creating your first workspace 🏗️\n", "3. Listing available workspaces 📋\n", "4. Adding PDF documents to the workspace 📄\n", "5. Creating and uploading a schema 📊\n", "6. Running PDF preprocessing 🔍\n", "7. Running LLM extractions 🤖\n", "\n", "![Extralit Workspace Management](https://raw.githubusercontent.com/argilla-io/argilla/main/docs/assets/argilla_workspace_management.png)\n", "\n", "## Introduction\n", "\n", "A **workspace** is a space inside your Extralit instance where authorized users can collaborate on datasets. Workspaces are accessible through both the Python SDK and the UI. When you first install Extralit, you'll need to create workspaces to organize your data and user access.\n", "\n", "For more details on workspace management, refer to the [Extralit documentation](https://docs.extralit.ai/latest/admin_guide/workspace/).\n", "\n", "Let's get started!\n"]}, {"cell_type": "markdown", "id": "5601c172", "metadata": {}, "source": ["## 1. Connecting to Extralit\n", "\n", "First, we need to import the Extralit library and connect to our instance using the default credentials."]}, {"cell_type": "code", "execution_count": 1, "id": "ce009aae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Successfully connected to Extralit at http://localhost:6900/\n"]}], "source": ["import extralit as ex\n", "import pandas as pd\n", "import pandera as pa\n", "from pandera.typing import Index, Series\n", "import tempfile\n", "from pathlib import Path\n", "\n", "# Connect to Extralit using default credentials\n", "client = ex.Extralit(api_url=\"http://localhost:6900/\", api_key=\"extralit.apikey\")\n", "\n", "print(f\"Successfully connected to Extralit at {client.api_url}\")"]}, {"cell_type": "markdown", "id": "fcdd3afe", "metadata": {}, "source": ["## 2. Creating Your First Workspace\n", "\n", "After connecting to Extralit, let's create our first workspace. We'll define a new `Workspace` object and call the `create()` method."]}, {"cell_type": "code", "execution_count": 4, "id": "64b5f09b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Workspace 'extralit' already exists. Using the existing workspace.\n"]}], "source": ["# Define a new workspace\n", "workspace_name = \"extralit\"\n", "try:\n", "    new_workspace = ex.Workspace(name=workspace_name, client=client)\n", "\n", "    # Create the workspace\n", "    workspace = new_workspace.create()\n", "\n", "    print(f\"Workspace '{workspace_name}' created successfully with ID: {workspace.id}\")\n", "except Exception:\n", "    print(f\"Workspace '{workspace_name}' already exists. Using the existing workspace.\")\n", "    workspace = client.workspaces(workspace_name)"]}, {"cell_type": "markdown", "id": "c937e5c7", "metadata": {}, "source": ["## 3. Listing Available Datsets\n", "\n", "Now, let's check all the Datatsts available in the workspace\n"]}, {"cell_type": "code", "execution_count": 7, "id": "d4df67d6", "metadata": {}, "outputs": [], "source": ["# List all workspaces\n", "dataset = client.datasets(\"papers-ocr-benchmarks_dataset\")\n", "dataset.settings.to_json(\"./datasets/imdb/settings.json\")"]}, {"cell_type": "code", "execution_count": 8, "id": "77a5690b", "metadata": {}, "outputs": [{"data": {"text/plain": ["Settings(guidelines=None, allow_extra_metadata=True, distribution=OverlapTaskDistribution(min_submitted=1), fields=[TextField(name=reference, title=reference, description=None, type=text, required=False) \n", ", TextField(name=type, title=type, description=None, type=text, required=False) \n", ", TextField(name=title, title=title, description=None, type=text, required=False) \n", ", TextField(name=issn, title=issn, description=None, type=text, required=False) \n", ", TextField(name=url, title=url, description=None, type=text, required=False) \n", ", TextField(name=doi, title=doi, description=None, type=text, required=False) \n", ", TextField(name=language, title=language, description=None, type=text, required=False) \n", ", TextField(name=urldate, title=urldate, description=None, type=text, required=False) \n", ", TextField(name=journal, title=journal, description=None, type=text, required=False) \n", ", TextField(name=authors, title=authors, description=None, type=text, required=False) \n", ", TextField(name=month, title=month, description=None, type=text, required=False) \n", ", TextField(name=note, title=note, description=None, type=text, required=False) \n", ", TextField(name=pages, title=pages, description=None, type=text, required=False) \n", ", TextField(name=filePaths, title=filePaths, description=None, type=text, required=False) \n", ", TextField(name=abstract, title=abstract, description=None, type=text, required=False) \n", ", TextField(name=copyright, title=copyright, description=None, type=text, required=False) \n", ", TextField(name=editor, title=editor, description=None, type=text, required=False) \n", "], questions=[RatingQuestion(name=rating_0, title=rating_0, description=None, type=rating, required=True) \n", "], vectors=[], metadata=[TermsMetadataProperty(name=reference, title=reference, visible_for_annotators=True), TermsMetadataProperty(name=doi, title=doi, visible_for_annotators=True)])"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["dataset.settings"]}, {"cell_type": "code", "execution_count": null, "id": "0cc75d87", "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "'reference'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                  <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[4]\u001b[39m\u001b[32m, line 1\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m1\u001b[39m \u001b[43mdataset\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_disk\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m./extralit-dataset/\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/extralit/extralit/src/extralit/datasets/_io/_disk.py:59\u001b[39m, in \u001b[36mDiskImportExportMixin.to_disk\u001b[39m\u001b[34m(self, path, with_records)\u001b[39m\n\u001b[32m     57\u001b[39m \u001b[38;5;28mself\u001b[39m.settings.to_json(path=settings_path)\n\u001b[32m     58\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m with_records:\n\u001b[32m---> \u001b[39m\u001b[32m59\u001b[39m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrecords\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_json\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrecords_path\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     61\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m path\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/extralit/extralit/src/extralit/records/_dataset_records.py:391\u001b[39m, in \u001b[36mDatasetRecords.to_json\u001b[39m\u001b[34m(self, path)\u001b[39m\n\u001b[32m    380\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mto_json\u001b[39m(\u001b[38;5;28mself\u001b[39m, path: Union[Path, \u001b[38;5;28mstr\u001b[39m]) -> Path:\n\u001b[32m    381\u001b[39m \u001b[38;5;250m    \u001b[39m\u001b[33;03m\"\"\"\u001b[39;00m\n\u001b[32m    382\u001b[39m \u001b[33;03m    Export the records to a file on disk.\u001b[39;00m\n\u001b[32m    383\u001b[39m \n\u001b[32m   (...)\u001b[39m\u001b[32m    389\u001b[39m \n\u001b[32m    390\u001b[39m \u001b[33;03m    \"\"\"\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m391\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_j<PERSON>\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/extralit/extralit/src/extralit/records/_dataset_records.py:159\u001b[39m, in \u001b[36mDatasetRecordsIterator.to_json\u001b[39m\u001b[34m(self, path)\u001b[39m\n\u001b[32m    158\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mto_json\u001b[39m(\u001b[38;5;28mself\u001b[39m, path: Union[Path, \u001b[38;5;28mstr\u001b[39m]) -> Path:\n\u001b[32m--> \u001b[39m\u001b[32m159\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mJsonIO\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_json\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrecords\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mlist\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mpath\u001b[49m\u001b[43m=\u001b[49m\u001b[43mpath\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/extralit/extralit/src/extralit/records/_io/_json.py:41\u001b[39m, in \u001b[36mJsonIO.to_json\u001b[39m\u001b[34m(records, path)\u001b[39m\n\u001b[32m     38\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m path.exists():\n\u001b[32m     39\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mFileExistsError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mFile \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpath\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m already exists.\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m---> \u001b[39m\u001b[32m41\u001b[39m record_dicts = \u001b[43mGenericIO\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_list\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrecords\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mflatten\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\n\u001b[32m     42\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m \u001b[38;5;28mopen\u001b[39m(path, \u001b[33m\"\u001b[39m\u001b[33mw\u001b[39m\u001b[33m\"\u001b[39m) \u001b[38;5;28;01mas\u001b[39;00m f:\n\u001b[32m     43\u001b[39m     json.dump(record_dicts, f)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/extralit/extralit/src/extralit/records/_io/_generic.py:41\u001b[39m, in \u001b[36mGenericIO.to_list\u001b[39m\u001b[34m(records, flatten)\u001b[39m\n\u001b[32m     39\u001b[39m dataset_records: \u001b[38;5;28mlist\u001b[39m = []\n\u001b[32m     40\u001b[39m \u001b[38;5;28;01mfor\u001b[39;00m record \u001b[38;5;129;01min\u001b[39;00m records:\n\u001b[32m---> \u001b[39m\u001b[32m41\u001b[39m     record_dict = \u001b[43mGenericIO\u001b[49m\u001b[43m.\u001b[49m\u001b[43m_record_to_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrecord\u001b[49m\u001b[43m=\u001b[49m\u001b[43mrecord\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mflatten\u001b[49m\u001b[43m=\u001b[49m\u001b[43mflatten\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     42\u001b[39m     records_schema.update([k \u001b[38;5;28;01mfor\u001b[39;00m k \u001b[38;5;129;01min\u001b[39;00m record_dict])\n\u001b[32m     43\u001b[39m     dataset_records.append(record_dict)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/extralit/extralit/src/extralit/records/_io/_generic.py:104\u001b[39m, in \u001b[36mGenericIO._record_to_dict\u001b[39m\u001b[34m(record, flatten)\u001b[39m\n\u001b[32m    101\u001b[39m     record_dict[\u001b[33m\"\u001b[39m\u001b[33mscore\u001b[39m\u001b[33m\"\u001b[39m] = score\n\u001b[32m    102\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m record_dict\n\u001b[32m--> \u001b[39m\u001b[32m104\u001b[39m record_dict = \u001b[43mrecord\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    105\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m flatten:\n\u001b[32m    106\u001b[39m     record_dict.update(\n\u001b[32m    107\u001b[39m         **record_dict.pop(\u001b[33m\"\u001b[39m\u001b[33mfields\u001b[39m\u001b[33m\"\u001b[39m, {}),\n\u001b[32m    108\u001b[39m         **record_dict.pop(\u001b[33m\"\u001b[39m\u001b[33mmetadata\u001b[39m\u001b[33m\"\u001b[39m, {}),\n\u001b[32m    109\u001b[39m         **record_dict.pop(\u001b[33m\"\u001b[39m\u001b[33mvectors\u001b[39m\u001b[33m\"\u001b[39m, {}),\n\u001b[32m    110\u001b[39m     )\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/extralit/extralit/src/extralit/records/_resource.py:199\u001b[39m, in \u001b[36mRecord.to_dict\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    197\u001b[39m server_id = \u001b[38;5;28mstr\u001b[39m(\u001b[38;5;28mself\u001b[39m._model.id) \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._model.id \u001b[38;5;28;01melse\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    198\u001b[39m status = \u001b[38;5;28mself\u001b[39m.status\n\u001b[32m--> \u001b[39m\u001b[32m199\u001b[39m fields = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfields\u001b[49m\u001b[43m.\u001b[49m\u001b[43mto_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    200\u001b[39m metadata = \u001b[38;5;28mself\u001b[39m.metadata.to_dict()\n\u001b[32m    201\u001b[39m suggestions = \u001b[38;5;28mself\u001b[39m.suggestions.to_dict()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/extralit/extralit/src/extralit/records/_resource.py:307\u001b[39m, in \u001b[36mRecordFields.to_dict\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    305\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m value \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m:\n\u001b[32m    306\u001b[39m     \u001b[38;5;28;01mcontinue\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m307\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_is_image\u001b[49m\u001b[43m(\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m)\u001b[49m:\n\u001b[32m    308\u001b[39m     fields[key] = cast_image(value)\n\u001b[32m    309\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._is_chat(key):\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Projects/extralit/extralit/src/extralit/records/_resource.py:322\u001b[39m, in \u001b[36mRecordFields._is_image\u001b[39m\u001b[34m(self, key)\u001b[39m\n\u001b[32m    320\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28mself\u001b[39m.record.dataset:\n\u001b[32m    321\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;01mF<PERSON><PERSON>\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m322\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mrecord\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdataset\u001b[49m\u001b[43m.\u001b[49m\u001b[43msettings\u001b[49m\u001b[43m.\u001b[49m\u001b[43mschema\u001b[49m\u001b[43m[\u001b[49m\u001b[43mkey\u001b[49m\u001b[43m]\u001b[49m.type == \u001b[33m\"\u001b[39m\u001b[33mimage\u001b[39m\u001b[33m\"\u001b[39m\n", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m: 'reference'"]}], "source": ["dataset.to_disk(\"./datasets/\")"]}, {"cell_type": "markdown", "id": "8ca5b2b8", "metadata": {}, "source": ["## 4. Adding PDF Documents to the Workspace\n", "\n", "Let's add two PDF documents to our workspace. For this tutorial, we'll create temporary PDF files. In a real-world scenario, you'd use actual scientific papers."]}, {"cell_type": "code", "execution_count": 10, "id": "59084a6d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created reference CSV at /tmp/references.csv\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>reference</th>\n", "      <th>file_path</th>\n", "      <th>title</th>\n", "      <th>authors</th>\n", "      <th>year</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>smith2023first</td>\n", "      <td>/tmp/sample1.pdf</td>\n", "      <td>Study on Sample Data</td>\n", "      <td><PERSON>, <PERSON>.</td>\n", "      <td>2023</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>johnson2022analysis</td>\n", "      <td>/tmp/sample2.pdf</td>\n", "      <td>Analysis of Experimental Results</td>\n", "      <td><PERSON>, <PERSON>.</td>\n", "      <td>2022</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             reference         file_path                             title  \\\n", "0       smith2023first  /tmp/sample1.pdf              Study on Sample Data   \n", "1  johnson2022analysis  /tmp/sample2.pdf  Analysis of Experimental Results   \n", "\n", "       authors  year  \n", "0    <PERSON>, J.  2023  \n", "1  Johnson, A.  2022  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["# In a real-world scenario, you would use actual PDFs. Here we'll create temp files\n", "# Define the paths for our temporary PDF files\n", "temp_dir = tempfile.gettempdir()\n", "pdf_file1 = Path(temp_dir) / \"sample1.pdf\"\n", "pdf_file2 = Path(temp_dir) / \"sample2.pdf\"\n", "\n", "# Create empty PDF files - in reality, these would be your actual PDFs\n", "with open(pdf_file1, \"wb\") as f:\n", "    f.write(b\"%PDF-1.5\\n%Example Document 1\")\n", "\n", "with open(pdf_file2, \"wb\") as f:\n", "    f.write(b\"%PDF-1.5\\n%Example Document 2\")\n", "\n", "# Create a reference dataframe with metadata for the PDFs\n", "references_df = pd.DataFrame(\n", "    {\n", "        \"reference\": [\"smith2023first\", \"johnson2022analysis\"],\n", "        \"file_path\": [str(pdf_file1), str(pdf_file2)],\n", "        \"title\": [\"Study on Sample Data\", \"Analysis of Experimental Results\"],\n", "        \"authors\": [\"<PERSON>, J<PERSON>\", \"<PERSON>, A.\"],\n", "        \"year\": [2023, 2022],\n", "    }\n", ")\n", "\n", "# Save the dataframe to a temporary CSV file\n", "references_csv = Path(temp_dir) / \"references.csv\"\n", "references_df.to_csv(references_csv, index=False)\n", "\n", "print(f\"Created reference CSV at {references_csv}\")\n", "references_df"]}, {"cell_type": "code", "execution_count": null, "id": "abc8562f", "metadata": {}, "outputs": [{"data": {"text/plain": ["Workspace(id=UUID('df91e2b9-c712-4474-af82-891478f23d40') inserted_at=datetime.datetime(2025, 4, 16, 1, 27, 58, 174591) updated_at=datetime.datetime(2025, 4, 16, 1, 27, 58, 174591) name='test-workspace')"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["new_workspace"]}, {"cell_type": "code", "execution_count": null, "id": "a147c6c2", "metadata": {}, "outputs": [], "source": ["# Import the documents into the workspace\n", "# For demonstration purposes, we'll use the extralit client directly\n", "# Initialize the extralit client with the same credentials\n", "extralit_client = ex.Extralit(\n", "    api_url=\"http://localhost:6900/\", api_key=\"extralit.apikey\"\n", ")\n", "\n", "# Import the documents\n", "result = extralit_client.import_documents(\n", "    workspace=workspace_name,\n", "    papers=str(references_csv),\n", "    metadatas=[\"title\", \"authors\", \"year\"],\n", ")\n", "\n", "print(f\"Imported {len(result)} documents into workspace '{workspace_name}'\")"]}, {"cell_type": "markdown", "id": "98a2025d", "metadata": {}, "source": ["## 5. Creating and Uploading a Schema\n", "\n", "Now, let's create a simple schema to define the structure of the data we want to extract from our documents."]}, {"cell_type": "code", "execution_count": null, "id": "f15f4b2f", "metadata": {}, "outputs": [], "source": ["from extralit.extraction.models.schema import SchemaStructure\n", "\n", "\n", "# Define a simple schema using Pandera\n", "class Publication(pa.DataFrameModel):\n", "    \"\"\"\n", "    General information about the publication, extracted once per paper.\n", "    \"\"\"\n", "\n", "    reference: Index[str] = pa.Field(unique=True, check_name=True)\n", "    title: Series[str] = pa.<PERSON>()\n", "    authors: Series[str] = pa.<PERSON>()\n", "    publication_year: Series[int] = pa.<PERSON>(ge=1900, le=2100)\n", "    doi: Series[str] = pa.Field(nullable=True)\n", "\n", "    class Config:\n", "        singleton = {\"enabled\": True}  # Indicates this is a document-level schema\n", "\n", "\n", "# Define a second schema for experimental data\n", "class ExperimentalData(pa.DataFrameModel):\n", "    \"\"\"\n", "    Experimental data extracted from the paper, may appear multiple times.\n", "    \"\"\"\n", "\n", "    experiment_id: Series[str] = pa.Field()\n", "    sample_size: Series[int] = pa.Field(gt=0)\n", "    study_type: Series[str] = pa.Field()\n", "    result_value: Series[float] = pa.Field()\n", "    significance: Series[float] = pa.Field(le=1.0, ge=0.0)\n", "\n", "\n", "# Create a schema structure object\n", "\n", "# Save schemas to a temporary JSON file\n", "schema_file = Path(temp_dir) / \"schemas.json\"\n", "schema_structure = SchemaStructure(\n", "    schemas={\"Publication\": Publication, \"ExperimentalData\": ExperimentalData}\n", ")\n", "schema_structure.to_json(schema_file)\n", "\n", "print(f\"Created schema file at {schema_file}\")"]}, {"cell_type": "code", "execution_count": null, "id": "dc61a1ab", "metadata": {}, "outputs": [], "source": ["# Upload the schema to the workspace\n", "result = extralit_client.upload_schemas(\n", "    workspace=workspace_name, schemas=str(schema_file)\n", ")\n", "\n", "print(f\"Uploaded schemas to workspace '{workspace_name}'\")"]}, {"cell_type": "markdown", "id": "7a92c20d", "metadata": {}, "source": ["## 6. Running PDF Preprocessing\n", "\n", "Next, let's run the PDF preprocessing step to extract text and table content from our documents."]}, {"cell_type": "code", "execution_count": null, "id": "768e7176", "metadata": {}, "outputs": [], "source": ["# Run PDF preprocessing\n", "from extralit.preprocessing.pdf import process_pdfs\n", "\n", "# Get the references from our dataframe\n", "references = references_df[\"reference\"].tolist()\n", "\n", "# Run the preprocessing step\n", "preprocessing_result = process_pdfs(\n", "    workspace=workspace_name,\n", "    references=references,\n", "    text_ocr=[\"default\"],  # Using the default text OCR model\n", "    table_ocr=[\"default\"],  # Using the default table OCR model\n", "    output_dataset=\"PDF_Preprocessing_Results\",\n", ")\n", "\n", "print(f\"Preprocessing completed for {len(preprocessing_result)} documents\")"]}, {"cell_type": "markdown", "id": "44aa776d", "metadata": {}, "source": ["## 7. Running LLM Extractions\n", "\n", "Finally, let's run the LLM extraction step to extract structured data according to our schema."]}, {"cell_type": "code", "execution_count": null, "id": "19221abe", "metadata": {}, "outputs": [], "source": ["# Run LLM extractions\n", "from extralit.extraction.llm import extract_data\n", "\n", "# Run the extraction step\n", "extraction_result = extract_data(\n", "    workspace=workspace_name,\n", "    references=references,\n", "    output_dataset=\"Data_Extraction_Results\",\n", ")\n", "\n", "print(f\"LLM extractions completed for {len(extraction_result)} documents\")"]}, {"cell_type": "markdown", "id": "c1b6ec61", "metadata": {}, "source": ["## 8. Checking Extraction Results\n", "\n", "Let's check the results of our extractions by listing the datasets created and viewing the extracted data."]}, {"cell_type": "code", "execution_count": null, "id": "4dbb695d", "metadata": {}, "outputs": [], "source": ["# List datasets in the workspace\n", "datasets = extralit_client.list_datasets(workspace=workspace_name)\n", "print(f\"Datasets in workspace '{workspace_name}':\\n\")\n", "for dataset in datasets:\n", "    print(f\"- {dataset.name} ({dataset.id})\")"]}, {"cell_type": "code", "execution_count": null, "id": "6eee019d", "metadata": {}, "outputs": [], "source": ["# Export the extracted data\n", "extracted_data = extralit_client.export_data(\n", "    workspace=workspace_name,\n", "    output=\"temp_output.csv\",  # This will save the data to a CSV file\n", ")\n", "\n", "# Display the extracted data\n", "if isinstance(extracted_data, dict):\n", "    for schema_name, data_df in extracted_data.items():\n", "        print(f\"\\nExtracted data for schema '{schema_name}':\\n\")\n", "        display(data_df)\n", "else:\n", "    print(\"\\nExtracted data:\")\n", "    display(extracted_data)"]}, {"cell_type": "markdown", "id": "7a30a5ea", "metadata": {}, "source": ["## Conclusion\n", "\n", "Congratulations! You've successfully tested the primary functionalities of Extralit with default credentials on a fresh install. You have:\n", "\n", "1. Connected to Extralit with default credentials\n", "2. Created a workspace\n", "3. Added PDF documents\n", "4. Created and uploaded a schema\n", "5. Run PDF preprocessing\n", "6. Run LLM extractions\n", "7. Checked the extraction results\n", "\n", "This workflow demonstrates the basic process of using Extralit for data extraction from scientific papers. In a real-world scenario, you would upload actual scientific papers and create more complex schemas tailored to your specific data extraction needs.\n", "\n", "For more detailed information, refer to the [Extralit documentation](https://docs.extralit.ai/latest/)."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}