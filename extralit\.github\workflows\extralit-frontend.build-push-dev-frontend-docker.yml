name: Build Extralit Docker image

on:
  workflow_call:
    inputs:
      download-python-package:
        description: "True if python package should be downloaded"
        type: boolean
        default: false
      image-name:
        description: "Name of the image to build"
        required: true
        type: string
      dockerfile:
        description: "Path to the Dockerfile to build"
        required: true
        type: string
      platforms:
        description: "Platforms to build for"
        required: true
        type: string
      build-args:
        description: "Build arguments"
        required: false
        type: string
        default: ""
      readme:
        description: "Path to the README file"
        required: false
        type: string
        default: "README.md"
    outputs:
      version:
        description: "Version of the Docker image"
        value: ${{ jobs.build.outputs.version }}
      google-docker-image:
        description: The name of the Docker image uploaded to Google Artifact Registry.
        value: ${{ jobs.build.outputs.google-docker-image }}

jobs:
  build:
    name: Build Docker image
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: extralit-frontend

    # Grant permissions to `GITHUB_TOKEN` for Google Cloud Workload Identity Provider
    permissions:
      contents: read
      id-token: write

    outputs:
      version: ${{ steps.docker-image-tag-from-ref.outputs.docker-image-tag }}
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18"

      - name: Build Frontend
        run: |
          npm install
          npm run build

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Get Docker image tag from GITHUB_REF
        id: docker-image-tag-from-ref
        uses: ./.github/actions/docker-image-tag-from-ref

      - name: Generate Docker tags
        id: generate-docker-tags
        run: |
          DOCKER_HUB_TAG="$IMAGE_NAME:$DOCKER_IMAGE_TAG"

          echo "tags=$DOCKER_HUB_TAG" >> $GITHUB_OUTPUT
        env:
          IMAGE_NAME: ${{ inputs.image-name }}
          DOCKER_IMAGE_TAG: ${{ steps.docker-image-tag-from-ref.outputs.docker-image-tag }}

      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.AR_DOCKER_USERNAME }}
          password: ${{ secrets.AR_DOCKER_PASSWORD }}

      # Authenticate in GCP using Workload Identity Federation, so we can push the Docker image to the Google Cloud Artifact Registry
      # - name: Authenticate to Google Cloud
      #   id: google-auth
      #   uses: "google-github-actions/auth@v1"
      #   with:
      #     token_format: access_token
      #     workload_identity_provider: ${{ secrets.GOOGLE_CLOUD_WIP }}
      #     service_account: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT }}

      # - name: Login to Google Artifact Registry
      #   uses: docker/login-action@v2
      #   with:
      #     registry: europe-docker.pkg.dev
      #     username: oauth2accesstoken
      #     password: ${{ steps.google-auth.outputs.access_token }}

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: extralit-frontend
          file: ${{ inputs.dockerfile }}
          platforms: ${{ inputs.platforms }}
          tags: ${{ steps.generate-docker-tags.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          build-args: ${{ inputs.build-args }}
          push: true
