name: Deploy Extralit environment

on:
  workflow_dispatch:
    inputs:
      image-name:
        description: The name of the Docker image to deploy.
        type: string
        default: extralit/extralit-quickstart-for-dev
      image-version:
        description: The version of the Docker image to deploy. In the form pr-<PR_NUMBER>.
        type: string

  workflow_call:
    inputs:
      image-name:
        description: The name of the Docker image to deploy.
        type: string
      image-version:
        description: The version of the Docker image to deploy. In the form pr-<PR_NUMBER>.
        type: string

jobs:
  deploy:
    name: Deploy Extralit to Cloud Run
    runs-on: ubuntu-latest

    # Grant permissions to `GITHUB_TOKEN` for Google Cloud Workload Identity Provider
    permissions:
      contents: read
      id-token: write
      pull-requests: write

    steps:
      - uses: actions/checkout@v4

      # Authenticate in GCP using Workload Identity Federation
      - name: Authenticate to Google Cloud
        uses: "google-github-actions/auth@v1"
        with:
          workload_identity_provider: ${{ secrets.GOOGLE_CLOUD_WIP }}
          service_account: ${{ secrets.GOOGLE_CLOUD_SERVICE_ACCOUNT }}

      - name: Generate credentials
        id: credentials
        uses: ./.github/actions/generate-credentials
        env:
          ENVIRONMENT_CREDENTIALS_SECRET: ${{ secrets.ENVIRONMENT_CREDENTIALS_SECRET }}

      - name: Deploy in Cloud Run
        id: deploy
        uses: "google-github-actions/deploy-cloudrun@v1"
        with:
          service: extralit-quickstart-${{ inputs.image-version }}
          image: europe-docker.pkg.dev/argilla-ci/${{ inputs.image-name}}:${{ inputs.image-version }}
          region: europe-southwest1
          flags: "--min-instances=1 --max-instances=1 --port=3000 --cpu=2000m --memory=4096Mi --no-cpu-throttling --allow-unauthenticated"
          env_vars: |
            OWNER_PASSWORD=${{ steps.credentials.outputs.owner }}
            OWNER_API_KEY=${{ steps.credentials.outputs.owner }}
            ADMIN_PASSWORD=${{ steps.credentials.outputs.admin }}
            ADMIN_API_KEY=${{ steps.credentials.outputs.admin }}
            ANNOTATOR_PASSWORD=${{ steps.credentials.outputs.annotator }}
            ANNOTATOR_API_KEY=${{ steps.credentials.outputs.annotator }}
            HF_HUB_DISABLE_TELEMETRY=1
            API_BASE_URL=https://dev.extralit.io/

      - name: Post credentials in Slack
        uses: ./.github/actions/slack-post-credentials
        if: ${{ github.event_name != 'workflow_dispatch' }}
        with:
          slack-channel-name: extralit-github
          url: ${{ steps.deploy.outputs.url }}
          owner: ${{ steps.credentials.outputs.owner }}
          admin: ${{ steps.credentials.outputs.admin }}
          annotator: ${{ steps.credentials.outputs.annotator }}
        env:
          SLACK_BOT_TOKEN: ${{ secrets.SLACK_BOT_TOKEN }}

      - name: Comment PR with Cloud Run URL
        uses: thollander/actions-comment-pull-request@v2
        if: ${{ github.event_name != 'workflow_dispatch' }}
        with:
          message: |
            The URL of the deployed environment for this PR is ${{ steps.deploy.outputs.url }}
          comment_tag: cloud_run_url
          reactions: rocket
