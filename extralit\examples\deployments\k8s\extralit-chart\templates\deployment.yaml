apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "extralit.fullname" . }}
  labels:
    {{- include "extralit.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.extralit.replicaCount }}
  selector:
    matchLabels:
      {{- include "extralit.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        {{- include "extralit.selectorLabels" . | nindent 8 }}
    spec:
      volumes:
      {{- if not .Values.elasticsearch.useOperator }}
        - name: hosts-file
          configMap:
            name: custom-hosts
      {{- end }}
      {{- if .Values.extralit.persistence.enabled }}
        - name: extralit-data
          persistentVolumeClaim:
            claimName: {{ .Release.Name }}-pvc
      {{- end }}
      containers:
        - name: {{ .Chart.Name }}
          image: "{{ .Values.extralit.image.repository }}:{{ .Values.extralit.image.tag }}"
          env:
            - name: EXTRALIT_ELASTICSEARCH
              # TODO: modify this to use externalElasticsearch.host if elasticsearch.useOperator is false
              value: {{if .Values.elasticsearch.useOperator}}"http://{{ include "extralit.fullname" . }}-es-http:9200"{{else}}"{{ .Values.externalElasticsearch.host }}:{{ .Values.externalElasticsearch.port }}"{{end}}
            - name: EXTRALIT_ELASTICSEARCH_SSL_VERIFY
              value: {{ .Values.elasticsearch.sslVerify | quote }}
            - name: EXTRALIT_AUTH_SECRET_KEY
              value: {{ .Values.extralit.authSecretKey | quote }}
            - name: EXTRALIT_REDIS_URL
              value: {{if .Values.externalRedis.enabled}}"{{ .Values.externalRedis.url }}"{{else}}"redis://{{ .Release.Name }}-redis-master:6379/0"{{end}}
            - name: EXTRALIT_REDIS_USE_CLUSTER
              value: {{ if and .Values.externalRedis.enabled .Values.externalRedis.is_redis_cluster }} "True" {{ else }} "False" {{ end }}
            - name: USERNAME
              value: {{ .Values.extralit.auth.username | quote }}
            - name: PASSWORD
              value: {{ .Values.extralit.auth.password  | quote }}
            - name: API_KEY
              value: {{ .Values.extralit.auth.apiKey  | quote }}
            {{- if .Values.extralit.persistence.enabled }}
            - name: EXTRALIT_HOME_PATH
              value: {{ .Values.extralit.persistence.mountPath | quote }}
            {{- end }}
          ports:
            - containerPort: 6900
          volumeMounts:
          {{- if not .Values.elasticsearch.useOperator }}
            - name: hosts-file
              mountPath: /etc/hosts
              subPath: hosts
          {{- end }}
          {{- if .Values.extralit.persistence.enabled }}
            - name: extralit-data
              mountPath: {{ .Values.extralit.persistence.mountPath}}
          {{- end }}
          resources:
            {{- toYaml .Values.extralit.resources | nindent 12 }}
