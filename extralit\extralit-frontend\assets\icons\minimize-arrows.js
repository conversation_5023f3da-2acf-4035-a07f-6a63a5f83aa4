/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'minimize-arrows': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<path pid="0" d="M9.493 5.69l3.69-3.64.817.827-3.678 3.63 2.076.009-.005 1.163-4.071-.016.016-4.072 1.163.005-.008 2.095zM5.661 9.48H3.585l-.001-1.163 4.071-.003.003 4.072H6.495l-.002-2.094-3.672 3.659L2 13.127l3.661-3.648z" _fill="#000"/>'
  }
})