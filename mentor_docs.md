Hey @<PERSON>riyank<PERSON>, here's some snippets of code that would be useful for building the `extralit documents embed --workspace workspace_name --reference reference`


```
client = Extralit.from_credentials()
workspace_obj = client.workspaces(name=workspace_name)

# this fetches all documents with the reference
documents = workspace_obj.documents(reference=reference)

# Check if a "chunks" dataset exists in the workspace, if not, create a Dataset
dataset = client.dataset(name="chunks", workspace=workspace_name)

for doc in documents:
  chunks = chunk_markdown(doc.metadata.text_extraction_metadata.markdown)

  records = create_records_from_chunks(doc, chunks)

  dataset.records.log(records)
```


You can see this delete document CLI command for the pattern https://github.com/Extralit/extralit/blob/develop/extralit/src/extralit/cli/documents/delete.py
Docs:
https://docs.extralit.ai/latest/admin_guide/dataset/#create-a-dataset
https://docs.extralit.ai/latest/admin_guide/record/


Jonny Tran
  3:55 AM
For the chunk_markdown  code, you can use the pymupdf4llm.LlamaMarkdownReader which probably has chunking methods built-in, or you can use this code snippet:

```
# this import shouldn't be in the `extralit/` client code, so you can copy this file from extralit-server/ to extralit/
from extralit_server.api.schemas.v1.document.chunks import Segments
from llama_index.core.schema import NodeRelationship, RelatedNodeInfo

def get_text_segments(pages: List[str], title="Title") -> Segments:
    segments = Segments()
    current_segment = None
    stored_header = ""
    parents_stack = []

        for line in page.split('\n'):
            header_match = re.match(r'(#+)\s*(.*)', line)
            if header_match:
                if current_segment and (current_segment.text or current_segment.relationships):
                    segments.items.append(current_segment)
                level = len(header_match.group(1))
                while parents_stack and parents_stack[-1].level >= level:
                    parents_stack.pop()
                parent = parents_stack[-1] if parents_stack else None
                current_segment = TextSegment(header=f"{stored_header}{header_match.group(2)}",
                                              level=level,
                                              page_number=page_number,
                                              text='')

                if parent:
                    current_segment.relationships[NodeRelationship.PARENT] = \
                        RelatedNodeInfo(node_id=parent.id, )

                    parent.relationships.setdefault(NodeRelationship.CHILD, []).append(
                        RelatedNodeInfo(node_id=current_segment.id, )
                    )
                stored_header = ""
                parents_stack.append(current_segment)

            elif current_segment:
                current_segment.text += line + '\n'

    return segments
    ```
