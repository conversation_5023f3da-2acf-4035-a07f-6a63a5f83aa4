/*!
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// Visually hide an element
%visuallyhidden {
  margin: -1px;
  padding: 0;
  width: 1px;
  height: 1px;
  overflow: hidden;
  clip: rect(0 0 0 0);
  clip: rect(0, 0, 0, 0);
  position: absolute;
}

// Align center block element
%center {
  display: block;
  margin-left: auto;
  margin-right: auto;
}

%vertical-center {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

// Clearfix
%clearfix {
  &:before,
  &:after {
    content: " ";
    display: table;
  }
  &:after {
    clear: both;
  }
}

// Input
%clear-input {
  width: 100%;
  background: none;
  outline: none;
  box-shadow: none;
  border: 0;
}

%container {
  margin: 0 auto;
  padding: 4em;
}

%clear {
  @include font-size(13px);
  background: none;
  min-width: auto;
  min-height: auto;
  width: auto;
  padding: 0;
  line-height: 1.3em;
  text-decoration: none;
  border: 0;
  &:hover,
  &:focus {
    text-decoration: none;
    background: none;
  }
}
%hide-scrollbar {
  /* Hide scrollbar for IE, Edge and Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
  /* Hide scrollbar for Chrome, Safari and Opera */
  &::-webkit-scrollbar {
    display: none;
  }
}
