/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'weak-labeling': {
    width: 40,
    height: 40,
    viewBox: '0 0 40 40',
    data: '<path pid="0" fill-rule="evenodd" clip-rule="evenodd" d="M32.397 14.794v10.412c0 .72-.583 1.302-1.302 1.302H13.367c-.38 0-.74-.166-.988-.455l-4.462-5.206a1.302 1.302 0 010-1.694l4.462-5.206c.247-.289.608-.455.988-.455h17.728c.72 0 1.302.583 1.302 1.302zm2.603 0v10.412a3.905 3.905 0 01-3.905 3.905H13.367a3.905 3.905 0 01-2.964-1.363L5.94 22.54a3.905 3.905 0 010-5.082l4.463-5.207a3.905 3.905 0 012.964-1.363h17.728A3.905 3.905 0 0135 14.794z" _fill="#000"/><path pid="1" fill-rule="evenodd" clip-rule="evenodd" d="M15.476 21.302a1.302 1.302 0 110-2.603 1.302 1.302 0 010 2.603zm0 2.603a3.905 3.905 0 110-7.81 3.905 3.905 0 010 7.81z" _fill="#000"/>'
  }
})