/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'danger': {
    width: 90,
    height: 82,
    viewBox: '0 0 90 82',
    data: '<path pid="0" d="M53.32 5.558a9.606 9.606 0 00-16.64 0L1.303 66.836a9.606 9.606 0 008.32 14.41h70.757a9.606 9.606 0 008.319-14.41L53.319 5.558zm30.182 67.884a3.543 3.543 0 01-3.123 1.804H9.62a3.607 3.607 0 01-3.123-5.41L41.876 8.558a3.606 3.606 0 016.248 0l35.378 61.278a3.542 3.542 0 010 3.606z" _fill="#000"/><path pid="1" d="M45 54.884a3 3 0 003-3V25.43a3 3 0 10-6 0v26.456a3 3 0 003 3zM45 60.917a3.175 3.175 0 100 6.35 3.175 3.175 0 000-6.35z" _fill="#000"/>'
  }
})