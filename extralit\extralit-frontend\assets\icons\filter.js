/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'filter': {
    width: 41,
    height: 40,
    viewBox: '0 0 41 40',
    data: '<path pid="0" d="M35.5 10A2.5 2.5 0 0033 7.5H8a2.5 2.5 0 000 5h25a2.5 2.5 0 002.5-2.5zM30.5 20a2.5 2.5 0 00-2.5-2.5H13a2.5 2.5 0 000 5h15a2.5 2.5 0 002.5-2.5zM23 27.5a2.5 2.5 0 010 5h-5a2.5 2.5 0 010-5h5z"/>'
  }
})