# Extralit GitHub labels configuration

- name: backend
  color: 0366d6
  description: Backend code and services

- name: deployment
  color: 0366d6
  description: Deployment and infrastructure

- name: documentation
  color: 0075ca
  description: Improvements or additions to documentation

- name: duplicate
  color: cfd3d4
  description: This issue or pull request already exists

- name: enhancement
  color: 0e8a16
  description: New feature or request

- name: epic
  color: 5319e7
  description: A large user story that can be broken down into smaller tasks

- name: good-first-issue
  color: 0e8a16
  description: Good for newcomers

- name: help-wanted
  color: 2cbe4e
  description: Extra attention is needed

- name: infrastructure
  color: 0366d6
  description: Infrastructure and deployment components

- name: invalid
  color: e4e669
  description: This doesn't seem right

- name: question
  color: d876e3
  description: Further information is requested

- name: refactor
  color: 1d76db
  description: Code refactoring or technical debt improvements

- name: ui/ux
  color: 0366d6
  description: User interface and experience

- name: wontfix
  color: ffffff
  description: This will not be worked on