/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'pen': {
    width: 16,
    height: 16,
    viewBox: '0 0 16 16',
    data: '<path pid="0" fill-rule="evenodd" clip-rule="evenodd" d="M14.279.717a.715.715 0 00-1.012 0l-.624.624a2.147 2.147 0 00-2.442.42L2.614 9.346l4.046 4.046 7.587-7.587c.66-.66.8-1.644.42-2.442l.623-.624a.715.715 0 000-1.012L14.28.717zm-3.053 6.087L6.66 11.37 4.637 9.347l4.566-4.566 2.023 2.023zm1.3-1.3l.71-.71a.715.715 0 000-1.01l-1.012-1.012a.715.715 0 00-1.012 0l-.708.709 2.023 2.023z" _fill="#000"/><path pid="1" d="M.5 15.493l1.518-5.564 4.046 4.046L.5 15.493z" _fill="#000"/>'
  }
})