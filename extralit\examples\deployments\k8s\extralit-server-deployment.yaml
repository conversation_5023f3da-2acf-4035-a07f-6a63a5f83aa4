apiVersion: apps/v1
kind: Deployment
metadata:
  name: extralit-server
  labels:
    app: extralit-server
spec:
  replicas: 1
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  selector:
    matchLabels:
      app: extralit-server
  template:
    metadata:
      labels:
        app: extralit-server
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
            - weight: 1
              preference:
                matchExpressions:
                  - key: role
                    operator: In
                    values:
                      - extralit-server
      initContainers:
        - name: wait-for-elasticsearch
          image: alpine/curl:latest
          command: [
              "sh",
              "-c",
              "ELASTICSEARCH_URL='http://elasticsearch-master:9200'; status_code=$(curl -s -k -o /dev/null -w '%{http_code}' $ELASTICSEARCH_URL);
              while [ $status_code -ne 200 ]; do sleep 5; status_code=$(curl -s -o /dev/null -w '%{http_code}' $ELASTICSEARCH_URL);
              echo Sleeping...; done; echo Elasticsearch Connected ",
            ]
      containers:
        - name: extralit-server
          image: extralit/extralit-server:latest
          ports:
            - containerPort: 6900
          resources:
            requests:
              cpu: "1"
              memory: "1Gi"
            limits:
              cpu: "2"
              memory: "2Gi"
          env:
            - name: EXTRALIT_ELASTICSEARCH_PROTOCOL
              value: "http"
            - name: EXTRALIT_ELASTICSEARCH_HOST
              value: "elasticsearch-master:9200"
            - name: EXTRALIT_ELASTIC_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: elasticsearch-master-credentials
                  key: password
                  optional: true
            - name: EXTRALIT_ELASTICSEARCH_SSL_VERIFY
              value: "false"
            # - name: EXTRALIT_ELASTICSEARCH_CA_PATH
            #   value: /usr/share/elasticsearch/config/certs/ca.crt
            - name: EXTRALIT_AUTH_SECRET_KEY
              value: "CHANGE_ME"
            - name: EXTRALIT_CORS_ORIGINS
              value: '["*"]'
            - name: EXTRALIT_REDIS_URL
              value: "redis://redis-master:6379/0"
            - name: EXTRALIT_EXTRALIT_URL
              value: "http://extralit-server:5555"
            - name: EXTRALIT_S3_ENDPOINT
              valueFrom:
                secretKeyRef:
                  name: extralit-secrets
                  key: S3_ENDPOINT
            - name: EXTRALIT_S3_ACCESS_KEY
              valueFrom:
                secretKeyRef:
                  name: extralit-secrets
                  key: S3_ACCESS_KEY
            - name: EXTRALIT_S3_SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: extralit-secrets
                  key: S3_SECRET_KEY
            - name: POSTGRES_HOST
              value: "main-db:5432"
            - name: POSTGRES_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: main-db
                  key: postgres-password
                  optional: true
          volumeMounts:
            - name: elasticsearch-master-certs
              mountPath: /usr/share/elasticsearch/config/certs
      volumes:
        - name: elasticsearch-master-certs
          secret:
            secretName: elasticsearch-master-certs
            optional: true
