/*
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/* eslint-disable */
var icon = require('vue-svgicon')
icon.register({
  'bulk-mode': {
    width: 24,
    height: 17,
    viewBox: '0 0 24 17',
    data: '<path pid="0" clip-rule="evenodd" d="M0 3a1 1 0 012 0 1 1 0 01-2 0zM5 4a1 1 0 010-2h18a1 1 0 110 2H5zM0 9a1 1 0 012 0 1 1 0 01-2 0zM5 10a1 1 0 010-2h18a1 1 0 110 2H5zM0 15a1 1 0 112 0 1 1 0 11-2 0zM5 16a1 1 0 110-2h18a1 1 0 110 2H5z"/>'
  }
})