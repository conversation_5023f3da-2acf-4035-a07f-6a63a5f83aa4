/*!
 * coding=utf-8
 * Copyright 2021-present, the Recognai S.L. team.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

// ===================================================================
// Flexbox Grid Mixins
// Version 0.1.3
// Description: Sass Mixins to generate Flexbox grid
// Author: thingsym
// GitHub: https://github.com/thingsym/flexbox-grid-mixins
// MIT License
// ===================================================================

@mixin grid($display: flex, $flex-direction: null, $flex-wrap: null, $flex-flow: null, $justify-content: null, $align-items: null, $align-content: null, $gutter: null, $grid-type: skeleton)
{
	box-sizing: border-box;

	@if $display {
		display: $display;
	}

	@if $flex-direction {
		flex-direction: $flex-direction;
	}
	@if $flex-wrap {
		flex-wrap: $flex-wrap;
	}

	@if $flex-flow {
		flex-flow: $flex-flow;
	}

	@if $justify-content {
		justify-content: $justify-content;
	}
	@if $align-items {
		align-items: $align-items;
	}
	@if $align-content {
		align-content: $align-content;
	}

	@if $grid-type == skeleton {
		@if $gutter {
			@include grid-margin($margin: 0 $gutter / 2 * -1);
		}
	}

	@content;
}

@mixin grid-col($col: null, $grid-columns: 12, $col-offset: null, $gutter: null, $condensed: false, $align-self: null, $flex-grow: 0, $flex-shrink: 1, $flex-basis: auto, $order: null, $grid-type: skeleton, $last-child: false)
{
	box-sizing: border-box;

	@if type-of($col) == number and unitless($col) == true {
		$flex-grow: 0;
		$flex-shrink: 0;
		$flex-basis: percentage($col / $grid-columns);

		@if $grid-type == skeleton {
			@if $gutter and unit($gutter) == '%' {
				$flex-basis: $flex-basis - $gutter;
			} @else if $gutter and unitless($gutter) == false {
				$flex-basis: calc( #{$flex-basis} - #{$gutter});
			}

		} @else if $grid-type == margin-offset {
			@if $gutter and unit($gutter) == '%' {
				$flex-basis: (100% - ($gutter * ($grid-columns / $col - 1))) / ($grid-columns / $col);
			} @else if $gutter and unitless($gutter) == false {
				$flex-basis: calc( #{$flex-basis} - #{$gutter * ($grid-columns / $col - 1) / ($grid-columns / $col)});
			}
		}

		@if $col-offset and unit($col-offset) == '%' {
			$flex-basis: $flex-basis + $col-offset;
		} @else if $col-offset and unitless($col-offset) == false {
			$flex-basis: calc( #{$flex-basis} + #{$col-offset});
		}
	} @else if type-of($col) == number and unitless($col) == false {
		$flex-grow: 0;
		$flex-shrink: 0;
		$flex-basis: $col;
	} @else if type-of($col) == string and $col == 'auto' {
		$flex-grow: 1;
		$flex-shrink: 1;
		$flex-basis: auto;
		max-width: 100%;
		width: auto;
	// flex: 1;
	} @else if type-of($col) == string and $col == 'equal' {
		$flex-grow: 1;
		$flex-shrink: 1;
		$flex-basis: 0;
	// flex: none;
	} @else if type-of($col) == string and $col == 'none' {
		$flex-grow: 0;
		$flex-shrink: 0;
		$flex-basis: auto;
	// flex: initial;
	} @else if type-of($col) == string and $col == 'initial' {
		$flex-grow: 0;
		$flex-shrink: 1;
		$flex-basis: auto;
	} @else if type-of($col) == string and $col == 'breakpoint' {
		$flex-grow: 0;
		$flex-shrink: 1;
		$flex-basis: auto;
		width: 100%;
	}

	flex: $flex-grow $flex-shrink $flex-basis;

	@if $align-self {
		align-self: $align-self;
	}

	@if type-of($order) == number {
		order: $order;
	}

	@if $gutter and unitless($gutter) == false {
		@if $grid-type == skeleton {
			@if $condensed == true {
				@include grid-col-margin($margin: 0 $gutter / 2);
			} @else {
				@include grid-col-margin($margin: 0 $gutter / 2 $gutter);
			}
		} @else if $grid-type == margin-offset {
			@if type-of($col) == string and $col == 'breakpoint' {
				@include grid-col-margin($margin-right: 0);
			} @else if $last-child {
				@include grid-col-margin($margin-right: 0);
			} @else {
				@include grid-col-margin($margin-right: $gutter);
			}

			@if $condensed == false {
				@include grid-col-margin($margin-bottom: $gutter);
			}
		}
	}

	@content;
}

@mixin grid-margin($margin: null, $margin-top: null, $margin-right: null, $margin-bottom: null, $margin-left: null)
{
	@if $margin != null {
		margin: $margin;
	}
	@if $margin-top != null {
		margin-top: $margin-top;
	}
	@if $margin-bottom != null {
		margin-bottom: $margin-bottom;
	}
	@if $margin-left != null {
		margin-left: $margin-left;
	}
	@if $margin-right != null {
		margin-right: $margin-right;
	}
}

@mixin grid-col-margin($margin: null, $margin-top: null, $margin-right: null, $margin-bottom: null, $margin-left: null)
{
	@include grid-margin($margin, $margin-top, $margin-right, $margin-bottom, $margin-left);
}